import { useTranslation } from 'react-i18next';
import { Version } from '@/utils/types';
import TagContainer from '@/components/shared/Tag/Tag';
import { Features, isFeatureActive } from '@/utils/featureFlags';
import KeyValueFieldArea, {
  FieldProps,
  FieldType,
} from '@/components/shared/KeyValueFieldArea/KeyValueFieldArea';
import { decodeBase64Checksum, getVersionChecksum } from '@/utils/checksum';

interface ChangeHistoryDetailsProps {
  version: Version;
}

const ChangeHistoryDetails: React.FC<ChangeHistoryDetailsProps> = ({ version }) => {
  const { t } = useTranslation();

  // Get and convert checksum
  const rawChecksum = getVersionChecksum(version);
  const displayChecksum = rawChecksum !== 'N/A' ? decodeBase64Checksum(rawChecksum) : 'N/A';

  const fields: FieldProps[] = [
    { label: t('version.username'), value: version.username },
    { label: t('version.archive'), value: '' },
    { label: t('version.checksum'), value: displayChecksum, fieldType: FieldType.idField },
    {
      label: t('version.changes'),
      value: version.changeReason,
      fieldType: FieldType.largeTextField,
    },
    { label: t('version.comments'), value: version.comment, fieldType: FieldType.largeTextField },
  ];

  return (
    <div className={'details-container mb-2'}>
      <div className="flex items-center gap-2 text-primary font-bold mb-2">
        {isFeatureActive(Features.TagContainer) && (
          <div className="mt-1">
            <TagContainer
              key={version.index}
              texts={['Release', 'Milestone', 'Bratwurst', 'Nudelsuppe']}
            />
          </div>
        )}
      </div>
      <KeyValueFieldArea fields={fields} />
    </div>
  );
};

export default ChangeHistoryDetails;
