#!/usr/bin/env node

/**
 * Generate SSL certificates for the current hostname
 * This script creates certificates that work with the dynamic Vite configuration
 */

import { execSync } from 'child_process';
import { existsSync, mkdirSync } from 'fs';
import os from 'os';
import path from 'path';

const hostname = os.hostname();
const certsDir = './certs';

console.log('🔐 Generating SSL certificates for hostname:', hostname);

// Ensure certs directory exists
if (!existsSync(certsDir)) {
  mkdirSync(certsDir, { recursive: true });
  console.log('📁 Created certs directory');
}

// Check if mkcert is available
try {
  execSync('mkcert -version', { stdio: 'ignore' });
  console.log('✅ mkcert is available');
} catch (error) {
  console.error('❌ mkcert is not installed or not in PATH');
  console.error('📋 To install mkcert:');
  console.error('   • Windows: choco install mkcert');
  console.error('   • macOS: brew install mkcert');
  console.error('   • Linux: See https://github.com/FiloSottile/mkcert#installation');
  process.exit(1);
}

// Install mkcert CA if not already done
try {
  console.log('🔧 Installing mkcert CA...');
  execSync('mkcert -install', { stdio: 'inherit' });
} catch (error) {
  console.warn('⚠️  Could not install mkcert CA (might already be installed)');
}

// Generate certificates
try {
  console.log(`🔑 Generating certificates for ${hostname}...`);

  // Change to certs directory
  process.chdir(certsDir);

  // Generate certificates for hostname
  execSync(`mkcert ${hostname}`, { stdio: 'inherit' });

  console.log('✅ Certificates generated successfully!');
  console.log(`📄 Certificate files created in ${path.resolve(certsDir)}:`);
  console.log(`   • ${hostname}.pem (certificate)`);
  console.log(`   • ${hostname}-key.pem (private key)`);

  console.log('\n🚀 You can now run your development server with:');
  console.log('   npm run dev');
  console.log(`\n🌐 Your app will be available at: https://${hostname}:3000`);
} catch (error) {
  console.error('❌ Failed to generate certificates:', error.message);
  process.exit(1);
}
