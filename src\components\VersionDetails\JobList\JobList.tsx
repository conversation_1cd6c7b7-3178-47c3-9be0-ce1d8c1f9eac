import React, { useCallback, useState, useMemo } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { VersionDetailsProps } from '../types';
import { Job } from '@/utils/types';
import JobResultsTable from './JobResultsTable/JobResultsTable';
import { executed as wasExecuted } from '@/utils/jobs';
import { GenericTable, HeaderCell } from '@/components/shared/GenericTable/GenericTable';
import Overlay from '@/components/shared/Overlay/Overlay';
import jobresultsSvg from '@/assets/svgs/light/job-results.svg';
import JobDetails from './JobDetails/JobDetails';
import { LatestCompareResults } from './LatestCompareResult/LatestCompareResult';
import { DateToString } from '@/utils/dateTime';
import ToolButton, { ToolButtonStyle } from '@/components/shared/ToolButton/ToolButton';
// Features import removed - JobExecution is now always available
import { useJobs } from '@/hooks/queries/useJobs';
import { LoadingSpinner } from '@/components/shared/LoadingSpinner/LoadingSpinner';
import { JobStatusProvider } from '@/context/JobStatusContext';
import JobStartStopButton from './JobStartStopButton';

// Import icons
import LatestResultsIcon from '@/assets/svgs/dark/results.svg';
import LastStartedIcon from '@/assets/svgs/dark/last-started.svg';
import jobSvg from '@/assets/svgs/dark/job.svg';
import { ToolTip } from '@/components/shared/ToolTip/ToolTip';
import CompareResult from '@/utils/compareResult';

interface JobListImagesCellProps {
  handleJobResultsRequested: (job: Job) => void;
  job: Job;
}

interface JobListCellProps {
  job: Job;
}

const anchorNameForJob = (job: Job): string => {
  return 'job-list-' + job.id + '-deactivated-anchor';
};

const classNameWithAnchor = (job: Job, className: string): string => {
  if (!job.isDeactivated) {
    return className;
  }
  return className + ' text-inverted-dark ' + anchorNameForJob(job);
};

const JobLastExecutedCell: React.FC<JobListCellProps> = ({ job }) => {
  const { t } = useTranslation();

  // Use optional chaining and nullish coalescing for cleaner code
  const lastExecutedText = () => {
    // Check if jobResults exists and has items
    if (wasExecuted(job)) {
      return DateToString(job.lastExecution);
    }
    return t('jobs.notExecuted');
  };

  return (
    <div className={classNameWithAnchor(job, 'h-full content-center')}>{lastExecutedText()}</div>
  );
};

const JobButtonsCell: React.FC<JobListImagesCellProps> = ({ handleJobResultsRequested, job }) => {
  const { t } = useTranslation();
  return (
    <div className={classNameWithAnchor(job, 'flex gap-medium')}>
      <ToolButton
        type={ToolButtonStyle.PrimaryBlue}
        icon={jobresultsSvg}
        active={job.latestResult.versionVsBackup != CompareResult.None}
        onClick={(e: React.MouseEvent) => {
          e.stopPropagation();
          handleJobResultsRequested(job);
        }}
      />
      <JobStartStopButton job={job} />
    </div>
  );
};

const JobResultsCell: React.FC<JobListCellProps> = ({ job }) => {
  return (
    <div className={classNameWithAnchor(job, 'h-full content-center')}>
      <LatestCompareResults deactivated={job.isDeactivated} results={job.latestResult} />
    </div>
  );
};

const JobNameCell: React.FC<JobListCellProps> = ({ job }) => {
  const { t } = useTranslation();
  return (
    <div className={classNameWithAnchor(job, 'h-full content-center')}>
      {job.name || t('common.notAvailable')}
    </div>
  );
};

const JobListContent: React.FC<VersionDetailsProps> = ({ details }) => {
  const { t } = useTranslation();
  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [overlayContent, setOverlayContent] = useState<React.ReactNode>(null);

  // Use the useJobs hook to fetch jobs data
  // Use nullish coalescing for cleaner code
  const { data: jobsData, isLoading, error } = useJobs(details?.id ?? null);

  const columns = [
    {
      header: () => <HeaderCell title={t('jobs.name')} icon={jobSvg} />,
      id: 'name',
      cell: (info: any) => {
        const job = info.row.original;
        return <JobNameCell job={job} />;
      },
    },
    {
      header: () => <HeaderCell title={t('jobs.latestResults')} icon={LatestResultsIcon} />,
      id: 'latestResults',
      cell: (info: any) => {
        const job = info.row.original as Job;
        return <JobResultsCell job={job} />;
      },
    },
    {
      header: () => <HeaderCell title={t('jobs.lastExecuted')} icon={LastStartedIcon} />,
      id: 'lastExecuted',
      cell: (info: any) => {
        const job = info.row.original;
        return <JobLastExecutedCell job={job} />;
      },
    },
    {
      header: '',
      id: 'jobResult',
      cell: (info: any) => {
        const job = info.row.original;
        return <JobButtonsCell handleJobResultsRequested={handleJobResultsRequested} job={job} />;
      },
    },
  ];

  const handleJobResultsRequested = useCallback((job: Job) => {
    unstable_batchedUpdates(() => {
      setOverlayContent(<JobResultsTable job={job} />);
      setIsOverlayOpen(true);
    });
  }, []);

  const closeOverlay = useCallback(() => {
    setIsOverlayOpen(false);
    setOverlayContent(null);
  }, []);

  const handleRowClick = useCallback((job: Job) => {
    return job.isDeactivated ? null : <JobDetails job={job} />;
  }, []);

  const noDataAvailable = !isLoading && !error && jobsData && jobsData.jobs.length <= 0;

  const jobToolTips = (
    <>
      {jobsData?.jobs.map((job: Job) => {
        return (
          job.isDeactivated && (
            <ToolTip
              anchor={anchorNameForJob(job)}
              title={t('jobs.deactivatedToolTipTitle')}
              text={job.deactivatedComment}
            />
          )
        );
      })}
    </>
  );

  return (
    <div className="mt-10">
      {isLoading && <LoadingSpinner />}
      {error && (
        <div className="text-red-500">
          {t('common.errorOccurred')}: {error.message}
        </div>
      )}
      {!isLoading && !error && jobsData && (
        <GenericTable
          data={jobsData?.jobs ?? []}
          columns={columns}
          renderSubComponent={handleRowClick}
          getRowCanExpand={(job: Job) => {
            return !job.isDeactivated;
          }}
          highlightCurrentRow={true}
        />
      )}
      {noDataAvailable && <div>{t('jobs.noJobs')}</div>}
      <Overlay isOpen={isOverlayOpen} onClose={closeOverlay} title={t('jobs.results')}>
        {overlayContent}
      </Overlay>
      {jobToolTips}
    </div>
  );
};

const JobList: React.FC<VersionDetailsProps> = props => {
  return (
    <JobStatusProvider>
      <JobListContent {...props} />
    </JobStatusProvider>
  );
};

export default JobList;
