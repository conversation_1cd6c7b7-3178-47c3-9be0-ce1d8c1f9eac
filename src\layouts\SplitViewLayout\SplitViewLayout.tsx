import React from 'react';
import { TreeContext } from '@/context/TreeContext';

interface SplitViewLayoutProps {
  sidebar: React.ReactNode;
  main: React.ReactNode;
}

const SplitViewLayout: React.FC<SplitViewLayoutProps> = ({ sidebar, main }) => {
  const treeContext = React.useContext(TreeContext);

  if (!treeContext) {
    throw new Error('SplitViewLayout must be used within a TreeProvider');
  }

  return (
    <div className="flex h-full w-full">
      {/* Always render the sidebar container, but control its width */}
      <div className="flex h-full">{sidebar}</div>
      <div className="flex-grow h-full min-h-0">{main}</div>
    </div>
  );
};

export default SplitViewLayout;
