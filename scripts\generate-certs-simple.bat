@echo off
echo Generating SSL certificates for development...

REM Create directories if they don't exist
if not exist "ssl\dev" mkdir ssl\dev
if not exist "ssl\prod" mkdir ssl\prod

REM Install mkcert root certificate
echo Installing mkcert root certificate...
mkcert -install

REM Generate certificates for localhost
echo Generating certificates for localhost...
cd ssl\dev
mkcert localhost 127.0.0.1 ::1

REM Rename the generated files to match our expected names
if exist "localhost+2.pem" (
    copy "localhost+2.pem" "localhost.crt"
    copy "localhost+2-key.pem" "localhost.key"
    del "localhost+2.pem"
    del "localhost+2-key.pem"
)

cd ..\..

echo.
echo SSL certificates generated successfully!
echo Files created:
echo   - ssl\dev\localhost.crt (certificate)
echo   - ssl\dev\localhost.key (private key)
echo.
echo Next steps:
echo 1. Update your configuration files as per the migration guide
echo 2. Test HTTPS in development: npm run dev:https
echo 3. Test Express server with HTTPS: npm run proxy:https
echo.
pause 