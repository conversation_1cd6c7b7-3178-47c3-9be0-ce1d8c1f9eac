import axios, { InternalAxiosRequestConfig } from 'axios';
import { toast } from 'react-toastify';
import { UserManager } from 'oidc-client-ts';
import { settings } from '@/authSettings';
import { refreshToken } from './tokenRefresh';
import { getConfig } from '@/config';

// Get runtime configuration
const config = getConfig();

// Direct API URLs - no proxy needed since CORS is configured on backend
const API_URL = config.API_URL;
const OAUTH_URL = config.OAUTH_URL;
const STORAGE_URL = config.STORAGE_URL;
const API_TIMEOUT = config.API_TIMEOUT;

// Ensure URLs end with trailing slash for consistent API paths
const API_BASE_URL = API_URL.endsWith('/') ? API_URL : `${API_URL}/`;
const OAUTH_BASE_URL = OAUTH_URL.endsWith('/') ? OAUTH_URL : `${OAUTH_URL}/`;
const STORAGE_BASE_URL = STORAGE_URL.endsWith('/') ? STORAGE_URL : `${STORAGE_URL}/`;

const OAUTH_API_URL = `${OAUTH_BASE_URL}v1/oauth2/`;
const DEFAULT_TIMEOUT = API_TIMEOUT;

// Create a single axios instance with consistent configuration
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: DEFAULT_TIMEOUT,
  timeoutErrorMessage: 'Request timed out. Please try again.',
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
});

// OAuth client for auth-specific endpoints
export const oauthClient = axios.create({
  baseURL: OAUTH_API_URL,
  timeout: DEFAULT_TIMEOUT,
});

// Storage client for storage endpoints
export const storageClient = axios.create({
  baseURL: STORAGE_BASE_URL,
  timeout: DEFAULT_TIMEOUT,
  timeoutErrorMessage: 'Request timed out. Please try again.',
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
});

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  response => response,
  error => Promise.reject(error)
);

// Add response interceptor for error handling (storageClient)
storageClient.interceptors.response.use(
  response => response,
  error => handleError(error)
);

// Add request interceptor
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig<any>) => config,
  error => Promise.reject(error)
);

// Flag to prevent multiple token refresh attempts at the same time
let isRefreshing = false;
// Queue of requests to retry after token refresh
let refreshQueue: Array<{
  resolve: (token: string) => void;
  reject: (error: any) => void;
}> = [];

// Process the queue of requests waiting for token refresh
const processQueue = (error: any, token: string | null = null) => {
  refreshQueue.forEach(promise => {
    if (error) {
      promise.reject(error);
    } else if (token) {
      promise.resolve(token);
    }
  });

  // Clear the queue
  refreshQueue = [];
};

// Enhanced error handler with token refresh capability
const handleError = async (error: any) => {
  const originalRequest = error.config;

  // Handle timeouts
  if (error.code === 'ECONNABORTED') {
    toast.error('Request timed out. Please check your connection.');
    return Promise.reject(error);
  }

  // Handle 401 Unauthorized errors - potential token expiration
  if (error.response?.status === 401 && !originalRequest._retry) {
    console.log('[API] 401 Unauthorized error detected, attempting token refresh');

    // Mark this request as already retried to prevent infinite loops
    originalRequest._retry = true;

    // If we're already refreshing a token, add this request to the queue
    if (isRefreshing) {
      console.log('[API] Token refresh already in progress, queuing request');
      return new Promise((resolve, reject) => {
        refreshQueue.push({
          resolve: token => {
            // Replace the old token with the new one
            originalRequest.headers['Authorization'] = `Bearer ${token}`;
            // Retry the request with the new token
            resolve(axios(originalRequest));
          },
          reject: err => {
            reject(err);
          },
        });
      });
    }

    // Start the token refresh process
    isRefreshing = true;

    try {
      // Try to refresh the token using our tokenRefresh service
      console.log('[API] Attempting to refresh token');
      const tokenResponse = await refreshToken();

      if (tokenResponse && tokenResponse.access_token) {
        const newToken = tokenResponse.access_token;
        console.log('[API] Token refresh successful');

        // Update the token in the original request
        originalRequest.headers['Authorization'] = `Bearer ${newToken}`;

        // Update the token in our API clients
        setAuthToken(newToken);

        // Process any queued requests with the new token
        processQueue(null, newToken);

        // Reset the refreshing flag
        isRefreshing = false;

        // Retry the original request with the new token
        return axios(originalRequest);
      } else {
        console.error('[API] Token refresh failed - no token returned');
        processQueue(new Error('Token refresh failed'));
        isRefreshing = false;

        // If silent refresh fails, try to use oidc-client's signinSilent as a fallback
        try {
          const userManager = new UserManager(settings);
          const user = await userManager.signinSilent();

          if (user && user.access_token) {
            console.log('[API] OIDC signinSilent successful');
            // Update the token in the original request
            originalRequest.headers['Authorization'] = `Bearer ${user.access_token}`;
            // Update the token in our API clients
            setAuthToken(user.access_token);
            // Retry the original request with the new token
            return axios(originalRequest);
          }
        } catch (oidcError) {
          console.error('[API] OIDC signinSilent failed:', oidcError);
          // Both refresh methods failed, redirect to login
          window.location.href = '/login';
          return Promise.reject(error);
        }
      }
    } catch (refreshError) {
      console.error('[API] Error during token refresh:', refreshError);
      processQueue(refreshError);
      isRefreshing = false;

      // If all refresh attempts fail, redirect to login
      window.location.href = '/login';
      return Promise.reject(error);
    }
  }

  // For all other errors, just reject the promise
  return Promise.reject(error);
};

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  response => response,
  error => handleError
);

// Add response interceptor for error handling
storageClient.interceptors.response.use(
  response => response,
  error => handleError
);

// Token Management
export const setAuthToken = (token: string) => {
  if (token) {
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    storageClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    delete apiClient.defaults.headers.common['Authorization'];
    delete storageClient.defaults.headers.common['Authorization'];
  }
};
