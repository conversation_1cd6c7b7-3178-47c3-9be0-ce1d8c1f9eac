#!/usr/bin/env node

/**
 * Configuration Manager
 * Helps developers manage runtime configuration for different environments
 */

import { copyFileSync, existsSync, readFileSync } from 'fs';
import { join } from 'path';

const CONFIG_DIR = 'public';
const CONFIGS = {
  development: 'config.development.js',
  staging: 'config.staging.js',
  production: 'config.js', // Default production config
};

function showUsage() {
  console.log('🔧 Configuration Manager\n');
  console.log('Usage: npm run config <environment>\n');
  console.log('Available environments:');
  console.log('  development  - Local development configuration');
  console.log('  staging      - Staging environment configuration');
  console.log('  production   - Production environment configuration');
  console.log('  list         - Show current configuration');
  console.log('  help         - Show this help\n');
  console.log('Examples:');
  console.log('  npm run config development  # Switch to development config');
  console.log('  npm run config staging      # Switch to staging config');
  console.log('  npm run config list         # Show current config');
}

function getCurrentConfig() {
  const configPath = join(CONFIG_DIR, 'config.js');
  if (!existsSync(configPath)) {
    return null;
  }

  try {
    const content = readFileSync(configPath, 'utf8');
    // Try to extract environment from the config
    const envMatch = content.match(/ENVIRONMENT:\s*['"]([^'"]+)['"]/);
    return envMatch ? envMatch[1] : 'unknown';
  } catch (error) {
    return 'error reading config';
  }
}

function switchConfig(environment) {
  const sourceFile = CONFIGS[environment];
  if (!sourceFile) {
    console.error(`❌ Unknown environment: ${environment}`);
    console.error('Available environments: development, staging, production');
    return false;
  }

  const sourcePath = join(CONFIG_DIR, sourceFile);
  const targetPath = join(CONFIG_DIR, 'config.js');

  if (!existsSync(sourcePath)) {
    console.error(`❌ Configuration file not found: ${sourcePath}`);
    return false;
  }

  try {
    copyFileSync(sourcePath, targetPath);
    console.log(`✅ Switched to ${environment} configuration`);
    console.log(`📁 Copied ${sourceFile} → config.js`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to copy configuration: ${error.message}`);
    return false;
  }
}

function listConfig() {
  const current = getCurrentConfig();
  console.log('📋 Current Configuration Status\n');

  if (current) {
    console.log(`Current environment: ${current}`);
  } else {
    console.log('❌ No config.js found');
  }

  console.log('\nAvailable configurations:');
  Object.entries(CONFIGS).forEach(([env, file]) => {
    const path = join(CONFIG_DIR, file);
    const exists = existsSync(path);
    const status = exists ? '✅' : '❌';
    console.log(`  ${status} ${env.padEnd(12)} (${file})`);
  });
}

// Main execution
const args = process.argv.slice(2);
const command = args[0];

switch (command) {
  case 'development':
  case 'staging':
  case 'production':
    switchConfig(command);
    break;
  case 'list':
    listConfig();
    break;
  case 'help':
  case undefined:
    showUsage();
    break;
  default:
    console.error(`❌ Unknown command: ${command}`);
    showUsage();
    process.exit(1);
}
