include:
  - component: $CI_SERVER_FQDN/auvesy-mdt/auvesy/versiondog/packages/ci-templates/git-version@main

default:
  image: node:18

stages:
  - .pre
  - security
  - publish

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_PIPELINE_SOURCE == 'web'
    - if: $CI_COMMIT_TAG =~ /^(?P<major>0|[1-9]\d*)\.(?P<minor>0|[1-9]\d*)\.(?P<patch>0|[1-9]\d*)$/
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH == 'develop'
    - if: $CI_COMMIT_BRANCH =~ /^release\/.*/
    - if: $CI_PIPELINE_SOURCE == 'pipeline'

# Global cache configuration for React project
cache:
  key: "${CI_COMMIT_REF_SLUG}"
  paths:
    # Dependencies
    - node_modules/
    - .npm/
    # Build output
    - build/
    - dist/
    # React/webpack cache
    - .cache/
    # Package manager lock file for cache validation
    - package-lock.json
  policy: pull-push

security:
  stage: security
  script:
    - npm install
    - npm audit || true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

publish:
  stage: publish
  needs:
    - optional: true
      job: security
  script:
    # Install dependencies
    - npm ci --production=false
    # Build for production
      # Set GitVersion environment variable for the Node.js script
    - export GitVersion_FullSemVer="${GitVersion_FullSemVer}"
    - export GitVersion_MajorMinorPatch="${GitVersion_MajorMinorPatch}"
    - export GitVersion_SemVer="${GitVersion_SemVer}"
    - echo " GitVersion variables available:"
    - echo "  GitVersion_FullSemVer=${GitVersion_FullSemVer}"
    - echo "  GitVersion_MajorMinorPatch=${GitVersion_MajorMinorPatch}"
    - echo "  GitVersion_SemVer=${GitVersion_SemVer}"
    - npm run build
    # Optional: can add environment-specific configuration here if needed
    # - cp .env.production .env
  variables:
    NODE_ENV: production
  artifacts:
    paths:
      - "build/*"
      - "dist/*"
    expire_in: 1 year
  rules:
    - if: $CI_COMMIT_TAG =~ /^(?P<major>0|[1-9]\d*)\.(?P<minor>0|[1-9]\d*)\.(?P<patch>0|[1-9]\d*)$/
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH == 'develop'
    - if: $CI_COMMIT_BRANCH =~ /^release\/.*/
    - if: $CI_PIPELINE_SOURCE == 'pipeline'
    - if: $CI_PIPELINE_SOURCE == 'web'
