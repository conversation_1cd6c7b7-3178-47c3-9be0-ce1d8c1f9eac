import React, { Suspense, useState } from 'react';
import { useTreeContext } from '@/context/TreeContext';
import { SplitViewLayout } from '@/layouts';
import TreeDetails from '../TreeDetails/TreeDetails';
import TreeViewToggle from '../../TreeViewToggle/TreeViewToggle';

const TreeView = React.lazy(() => import('../TreeView/TreeView'));

const TreeContainer: React.FC = () => {
  const { error, loading, data = [], isTreeVisible = true } = useTreeContext();
  const [searchTerm, setSearchTerm] = useState('');

  // Prepare the main content (TreeDetails) which can render without tree data
  const main = (
    <div className="h-full bg-background">
      <TreeDetails />
    </div>
  );

  // Create sidebar content based on state
  let sidebarContent;

  if (error) {
    sidebarContent = (
      <div className="flex-center w-full text-error p-4">
        {typeof error === 'string' ? error : 'Error loading tree data'}
      </div>
    );
  } else {
    // Always show TreeView - it will handle its own loading state internally
    sidebarContent = (
      <Suspense
        fallback={
          <div className="flex-center w-full h-full">
            <div className="text-center">
              <div className="spinner-primary spinner-lg mb-4" />
              <p className="text-border-color-dark text-sm">Loading tree...</p>
            </div>
          </div>
        }
      >
        <TreeView searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
      </Suspense>
    );
  }

  // Create the sidebar with the tree view
  const sidebar = (
    <div className="flex">
      <div
        className={`bg-background flex flex-col overflow-x-hidden ${
          isTreeVisible
            ? 'w-[420px] opacity-100 transition-all duration-300'
            : 'w-0 opacity-0 invisible transition-opacity duration-100'
        }`}
      >
        {sidebarContent}
      </div>
      <div className="flex items-center justify-center bg-background">
        <TreeViewToggle />
      </div>
    </div>
  );

  return <SplitViewLayout sidebar={sidebar} main={main} />;
};

export default React.memo(TreeContainer);
