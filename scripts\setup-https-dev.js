#!/usr/bin/env node

/**
 * Complete HTTPS development setup script
 * 1. Shows hostname
 * 2. Generates certificates for the hostname
 * 3. Starts the development server with HTTPS
 */

import { execSync, spawn } from 'child_process';
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs';
import os from 'os';
import path from 'path';

const hostname = os.hostname();
const certsDir = './certs';
const envFile = './.env';

console.log('🚀 HTTPS Development Setup');
console.log('='.repeat(50));

// Check if dependencies are installed
if (!existsSync('./node_modules')) {
  console.error('❌ Dependencies not installed!');
  console.error('📋 Please run this first:');
  console.error('   npm install');
  console.error('');
  console.error('💡 Then run the setup again:');
  console.error('   npm run setup-https');
  process.exit(1);
}

// Step 1: Show hostname
console.log('🔍 Step 1: Detecting hostname...');
console.log(`✅ Your hostname is: ${hostname}`);

// Step 2: Ensure certs directory exists
console.log('\n📁 Step 2: Setting up certificates directory...');
if (!existsSync(certsDir)) {
  mkdirSync(certsDir, { recursive: true });
  console.log('✅ Created certs directory');
} else {
  console.log('✅ Certs directory already exists');
}

// Step 3: Check if certificates already exist
const certFile = path.join(certsDir, `${hostname}.pem`);
const keyFile = path.join(certsDir, `${hostname}-key.pem`);

console.log('\n🔐 Step 3: Checking for existing certificates...');
if (existsSync(certFile) && existsSync(keyFile)) {
  console.log('✅ Certificates already exist for this hostname');
  console.log(`   • Certificate: ${certFile}`);
  console.log(`   • Private Key: ${keyFile}`);
} else {
  console.log('📝 Certificates not found, generating new ones...');

  // Check if mkcert is available
  try {
    execSync('mkcert -version', { stdio: 'ignore' });
    console.log('✅ mkcert is available');
  } catch (error) {
    console.error('❌ mkcert is not installed or not in PATH');
    console.error('📋 To install mkcert:');
    console.error('   • Windows: choco install mkcert');
    console.error('   • macOS: brew install mkcert');
    console.error('   • Linux: See https://github.com/FiloSottile/mkcert#installation');
    process.exit(1);
  }

  // Install mkcert CA if not already done
  try {
    console.log('🔧 Installing mkcert CA...');
    execSync('mkcert -install', { stdio: 'inherit' });
  } catch (error) {
    console.warn('⚠️  Could not install mkcert CA (might already be installed)');
  }

  // Generate certificates
  try {
    console.log(`🔑 Generating certificates for ${hostname}...`);

    // Change to certs directory and generate certificates
    const originalCwd = process.cwd();
    process.chdir(certsDir);

    execSync(`mkcert ${hostname}`, { stdio: 'inherit' });

    // Change back to original directory
    process.chdir(originalCwd);

    console.log('✅ Certificates generated successfully!');
  } catch (error) {
    console.error('❌ Failed to generate certificates:', error.message);
    process.exit(1);
  }
}

// Step 4: Update .env file
console.log('\n⚙️  Step 4: Updating environment configuration...');
if (existsSync(envFile)) {
  let envContent = readFileSync(envFile, 'utf8');

  // Update or add VITE_HOSTNAME
  if (envContent.includes('VITE_HOSTNAME=')) {
    envContent = envContent.replace(/VITE_HOSTNAME=.*/g, `VITE_HOSTNAME=${hostname}`);
    console.log('✅ Updated VITE_HOSTNAME in .env');
  } else {
    envContent = `VITE_HOSTNAME=${hostname}\n` + envContent;
    console.log('✅ Added VITE_HOSTNAME to .env');
  }

  // Ensure HTTP flags are commented out for HTTPS mode
  envContent = envContent.replace(/^VITE_FORCE_HTTP=true/gm, '# VITE_FORCE_HTTP=true');
  envContent = envContent.replace(/^VITE_ALLOW_HTTP=true/gm, '# VITE_ALLOW_HTTP=true');

  writeFileSync(envFile, envContent);
  console.log('✅ Configured .env for HTTPS mode');
} else {
  console.log('⚠️  .env file not found, creating basic configuration...');
  const basicEnv = `VITE_HOSTNAME=${hostname}
PORT=3000

# HTTP/HTTPS Configuration for development server
# VITE_FORCE_HTTP=true          # Force HTTP mode (skip certificate search)
# VITE_ALLOW_HTTP=true          # Allow HTTP fallback if no certificates found

# Note: Backend URLs are now configured in public/config.js
# Run 'npm run config development' to set up application configuration
`;
  writeFileSync(envFile, basicEnv);
  console.log('✅ Created .env file with HTTPS configuration');
  console.log(
    '💡 Remember to run "npm run config development" to set up application configuration'
  );
}

// Step 5: Show configuration summary
console.log('\n📋 Step 5: Configuration Summary');
console.log('='.repeat(30));
console.log(`🌐 Hostname: ${hostname}`);
console.log(`🔒 Certificate: ${certFile}`);
console.log(`🔑 Private Key: ${keyFile}`);
console.log(`📄 Environment: ${envFile}`);
console.log(`🚀 Frontend URL: https://${hostname}:3000`);

// Step 6: Start development server
console.log('\n🚀 Step 6: Starting HTTPS development server...');
console.log('='.repeat(40));
console.log('Press Ctrl+C to stop the server');
console.log('');

try {
  // Start the development server
  const devServer = spawn('npm', ['run', 'dev'], {
    stdio: 'inherit',
    shell: process.platform === 'win32',
  });

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n👋 Shutting down development server...');
    devServer.kill('SIGINT');
    process.exit(0);
  });

  devServer.on('close', code => {
    console.log(`\n📊 Development server exited with code ${code}`);
    process.exit(code);
  });
} catch (error) {
  console.error('❌ Failed to start development server:', error.message);
  process.exit(1);
}
