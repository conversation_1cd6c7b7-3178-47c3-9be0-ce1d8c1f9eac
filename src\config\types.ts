/**
 * Runtime Configuration Types
 * Defines the structure of the runtime configuration loaded from config.js
 */

export interface AppConfig {
  // Core API Configuration
  API_URL: string;
  OAUTH_URL: string;
  STORAGE_URL: string;
  API_TIMEOUT: number;

  // Feature Flags
  EXPERIMENTAL: boolean;

  // Optional Configuration
  HOSTNAME?: string;
  PORT?: number;

  // Cache Configuration (optional)
  CACHE_ENABLED?: boolean;
  CACHE_SIZE_MB?: number;
  CACHE_MAX_AGE?: number;

  // Application Metadata
  APP_VERSION?: string;

  // Environment Configuration
  ENVIRONMENT?: 'development' | 'production' | 'test';

  // Development/Debug Configuration
  DEBUG_MODE?: boolean;
  FORCE_HTTP?: boolean;
  ALLOW_HTTP?: boolean;

  // Logging Configuration
  LOG_LEVEL?: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';

  // OAuth Client Configuration
  OAUTH_CLIENT_ID?: string;
  OAUTH_REDIRECT_URI?: string;

  // Additional Runtime Settings
  THEME?: string;
  LANGUAGE?: string;
}

/**
 * Configuration validation result
 */
export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Configuration loading options
 */
export interface ConfigLoadOptions {
  /** Whether to throw errors on validation failure (default: true) */
  throwOnError?: boolean;
  /** Whether to log configuration details (default: true in development) */
  enableLogging?: boolean;
  /** Fallback configuration to use if loading fails */
  fallbackConfig?: Partial<AppConfig>;
}
