/// <reference types="vite/client" />

// Runtime configuration is now handled through window.APP_CONFIG
// Build-time environment variables are no longer used for application configuration
interface ImportMetaEnv {
  // Only keep essential build-time variables
  readonly MODE: string;
  readonly PROD: boolean;
  readonly DEV: boolean;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// Runtime Configuration Types
interface AppConfig {
  // Core API Configuration
  API_URL: string;
  OAUTH_URL: string;
  STORAGE_URL: string;
  API_TIMEOUT: number;

  // Feature Flags
  EXPERIMENTAL: boolean;

  // Optional Configuration
  HOSTNAME?: string;
  PORT?: number;

  // Cache Configuration (optional)
  CACHE_ENABLED?: boolean;
  CACHE_SIZE_MB?: number;
  CACHE_MAX_AGE?: number;

  // Application Metadata
  APP_VERSION?: string;

  // Development/Debug Configuration
  DEBUG_MODE?: boolean;
  FORCE_HTTP?: boolean;
  ALLOW_HTTP?: boolean;
}

// Global window object extension
declare global {
  interface Window {
    APP_CONFIG?: Partial<AppConfig>;
  }
}
