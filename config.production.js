// Production Runtime Configuration Template
// Copy this file to the web server's public directory as 'config.js'
// Edit these values for your specific production environment

window.APP_CONFIG = {
  // Core API Configuration - REQUIRED
  // Replace with your actual production server URLs
  API_URL: 'https://your-api-server.domain.com/api/',
  OAUTH_URL: 'https://your-oauth-server.domain.com/',
  STORAGE_URL: 'https://your-storage-server.domain.com/Files',

  // API timeout in milliseconds
  API_TIMEOUT: 15000,

  // Feature Flags
  EXPERIMENTAL: false, // Set to true to enable experimental features

  // Optional Configuration
  HOSTNAME: 'your-production-hostname',
  PORT: 443, // or 80 for HTTP

  // Cache Configuration (optional)
  CACHE_ENABLED: true,
  CACHE_SIZE_MB: 100,
  CACHE_MAX_AGE: 3600000, // 1 hour in milliseconds

  // Application Metadata (optional)
  APP_VERSION: '1.0.0', // Will be automatically set during build if not specified

  // Development/Debug Configuration
  DEBUG_MODE: false, // Set to true for additional logging
  FORCE_HTTP: false, // Set to true to force HTTP (not recommended for production)
  ALLOW_HTTP: false, // Set to true to allow HTTP fallback
};

// Production logging (minimal)
if (window.console && window.console.log) {
  console.log('[Config] Production configuration loaded');
}
