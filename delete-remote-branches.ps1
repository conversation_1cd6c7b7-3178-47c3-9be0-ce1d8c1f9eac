# PowerShell script to delete remote branches except specified ones
# Usage: .\delete-remote-branches.ps1

# Define branches to KEEP (modify this list as needed)
$branchesToKeep = @(
    "main",
    "develop",
    "master"
    # Add other branches you want to keep here
)

Write-Host "Branches to keep: $($branchesToKeep -join ', ')" -ForegroundColor Green

# Get all remote branches
$remoteBranches = git branch -r | ForEach-Object { $_.Trim() } | Where-Object { $_ -notlike "origin/HEAD*" }

Write-Host "`nFound remote branches:" -ForegroundColor Yellow
$remoteBranches | ForEach-Object { Write-Host "  $_" }

# Filter branches to delete (remove 'origin/' prefix for comparison)
$branchesToDelete = $remoteBranches | Where-Object {
    $branchName = $_.Replace("origin/", "")
    $branchesToKeep -notcontains $branchName
}

if ($branchesToDelete.Count -eq 0) {
    Write-Host "`nNo branches to delete!" -ForegroundColor Green
    exit
}

Write-Host "`nBranches to delete:" -ForegroundColor Red
$branchesToDelete | ForEach-Object { Write-Host "  $_" }

# Ask for confirmation
$confirmation = Read-Host "`nDo you want to delete these branches? (y/N)"
if ($confirmation -ne "y" -and $confirmation -ne "Y") {
    Write-Host "Operation cancelled." -ForegroundColor Yellow
    exit
}

# Delete branches
Write-Host "`nDeleting branches..." -ForegroundColor Yellow
foreach ($branch in $branchesToDelete) {
    $branchName = $branch.Replace("origin/", "")
    Write-Host "Deleting $branchName..." -ForegroundColor Cyan
    git push origin --delete $branchName
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✓ Deleted $branchName" -ForegroundColor Green
    } else {
        Write-Host "  ✗ Failed to delete $branchName" -ForegroundColor Red
    }
}

Write-Host "`nOperation completed!" -ForegroundColor Green 