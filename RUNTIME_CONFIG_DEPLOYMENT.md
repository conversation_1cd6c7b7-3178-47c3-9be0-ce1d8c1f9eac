# Runtime Configuration Deployment Guide

## Overview

This application now uses runtime configuration instead of build-time environment variables. This allows a single build to be deployed across multiple environments by simply changing the `config.js` file.

## How It Works

1. **Build Time**: The application is built once without environment-specific values
2. **Runtime**: Configuration is loaded from `/config.js` when the application starts
3. **Deployment**: Each environment has its own `config.js` file with appropriate values

## Deployment Steps

### 1. Build the Application

```bash
npm install
npm run build
```

This creates a `dist/` directory with the built application.

### 2. Prepare Configuration

Copy the appropriate configuration template:

```bash
# For production
cp config.production.js dist/config.js

# Or create a custom config.js file
```

### 3. Edit Configuration

Edit `dist/config.js` with your environment-specific values:

```javascript
window.APP_CONFIG = {
  // REQUIRED: Update these URLs for your environment
  API_URL: 'https://your-api-server.domain.com/api/',
  OAUTH_URL: 'https://your-oauth-server.domain.com/',
  STORAGE_URL: 'https://your-storage-server.domain.com/Files',
  
  // Optional: Adjust other settings as needed
  API_TIMEOUT: 15000,
  EXPERIMENTAL: false,
  // ... other settings
};
```

### 4. Deploy to Web Server

Copy the entire `dist/` directory to your web server (IIS, nginx, Apache, etc.).

## Configuration Reference

### Required Settings

| Setting | Description | Example |
|---------|-------------|---------|
| `API_URL` | Main API endpoint | `https://api.company.com/api/` |
| `OAUTH_URL` | OAuth authentication server | `https://auth.company.com/` |
| `STORAGE_URL` | File storage service | `https://files.company.com/Files` |
| `API_TIMEOUT` | Request timeout in milliseconds | `15000` |
| `EXPERIMENTAL` | Enable experimental features | `false` |

### Optional Settings

| Setting | Description | Default |
|---------|-------------|---------|
| `HOSTNAME` | Server hostname | Auto-detected |
| `PORT` | Server port | Auto-detected |
| `CACHE_ENABLED` | Enable client-side caching | `false` |
| `CACHE_SIZE_MB` | Cache size limit | `50` |
| `CACHE_MAX_AGE` | Cache expiration time | `3600000` |
| `DEBUG_MODE` | Enable debug logging | `false` |

## Environment Examples

### Development
```javascript
window.APP_CONFIG = {
  API_URL: 'https://dev-api.company.com/api/',
  OAUTH_URL: 'https://dev-auth.company.com/',
  STORAGE_URL: 'https://dev-files.company.com/Files',
  API_TIMEOUT: 10000,
  EXPERIMENTAL: true,
  DEBUG_MODE: true,
};
```

### Staging
```javascript
window.APP_CONFIG = {
  API_URL: 'https://staging-api.company.com/api/',
  OAUTH_URL: 'https://staging-auth.company.com/',
  STORAGE_URL: 'https://staging-files.company.com/Files',
  API_TIMEOUT: 15000,
  EXPERIMENTAL: false,
  DEBUG_MODE: false,
};
```

### Production
```javascript
window.APP_CONFIG = {
  API_URL: 'https://api.company.com/api/',
  OAUTH_URL: 'https://auth.company.com/',
  STORAGE_URL: 'https://files.company.com/Files',
  API_TIMEOUT: 15000,
  EXPERIMENTAL: false,
  DEBUG_MODE: false,
};
```

## Troubleshooting

### Configuration Not Loading

1. Verify `config.js` exists in the web server's public directory
2. Check browser console for loading errors
3. Ensure `config.js` has valid JavaScript syntax

### Invalid Configuration

The application will show an error message if configuration is invalid. Check:

1. All required fields are present
2. URLs are properly formatted
3. Numeric values are valid numbers
4. Boolean values are `true` or `false`

### Network Issues

If APIs are not accessible:

1. Verify URLs are correct and accessible
2. Check CORS configuration on backend servers
3. Verify SSL certificates if using HTTPS
4. Check firewall and network connectivity

## Migration from Environment Variables

This application previously used build-time environment variables. The mapping is:

| Old Environment Variable | New Config Property |
|-------------------------|-------------------|
| `VITE_API_URL` | `API_URL` |
| `VITE_OAUTH_URL` | `OAUTH_URL` |
| `VITE_STORAGE_URL` | `STORAGE_URL` |
| `VITE_API_TIMEOUT` | `API_TIMEOUT` |
| `VITE_EXPERIMENTAL` | `EXPERIMENTAL` |
| `VITE_HOSTNAME` | `HOSTNAME` |

## Security Considerations

1. **No Secrets**: Never put sensitive information (passwords, private keys) in `config.js`
2. **HTTPS**: Always use HTTPS URLs in production
3. **Validation**: The application validates configuration on startup
4. **Access Control**: Ensure proper access controls on backend APIs
