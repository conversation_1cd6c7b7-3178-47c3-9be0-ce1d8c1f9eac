/**
 * Decodes a base64 checksum string to hexadecimal representation
 * @param base64Checksum - The base64 encoded checksum string
 * @returns The hexadecimal representation of the checksum
 */
export const decodeBase64Checksum = (base64Checksum: string): string => {
  try {
    if (!base64Checksum || base64Checksum === 'N/A') {
      return 'N/A';
    }

    // Use browser's built-in atob for Base64 decoding
    const decodedString = atob(base64Checksum);

    // Convert to hexadecimal representation
    let checksumHex = '';
    for (let i = 0; i < decodedString.length; i++) {
      const hex = decodedString.charCodeAt(i).toString(16);
      checksumHex += hex.length === 1 ? '0' + hex : hex;
    }

    return checksumHex;
  } catch (error) {
    console.error('Error decoding base64 checksum:', error);
    return 'Invalid checksum';
  }
};

/**
 * Gets the checksum from version object as raw text
 * @param version - The version object
 * @returns The raw checksum value without any transformations
 */
export const getVersionChecksum = (version: any): string => {
  // Get checksum value directly from the version object
  const checksum = version?.checksum;

  // If no checksum field at all, return N/A
  if (checksum === undefined || checksum === null || checksum === '') {
    return 'N/A';
  }

  // Just return the raw checksum text as-is, no transformations
  return String(checksum);
};
