# SSL Certificate Generation Script for Windows Development
# This script generates self-signed SSL certificates for local development

param(
    [switch]$Force
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

Write-ColorOutput "Generating SSL certificates for development..." $Green

# Create directories
$sslDevPath = "ssl\dev"
$sslProdPath = "ssl\prod"

if (!(Test-Path $sslDevPath)) {
    New-Item -ItemType Directory -Path $sslDevPath -Force | Out-Null
}

if (!(Test-Path $sslProdPath)) {
    New-Item -ItemType Directory -Path $sslProdPath -Force | Out-Null
}

# Check if OpenSSL is available
$opensslPath = $null

# Try to find OpenSSL in common locations
$possiblePaths = @(
    "C:\Program Files\OpenSSL-Win64\bin\openssl.exe",
    "C:\Program Files (x86)\OpenSSL-Win64\bin\openssl.exe",
    "C:\OpenSSL-Win64\bin\openssl.exe",
    "C:\OpenSSL-Win32\bin\openssl.exe"
)

foreach ($path in $possiblePaths) {
    if (Test-Path $path) {
        $opensslPath = $path
        break
    }
}

# Check if OpenSSL is in PATH
if (!$opensslPath) {
    try {
        $opensslPath = (Get-Command openssl -ErrorAction Stop).Source
    }
    catch {
        # OpenSSL not found
    }
}

if (!$opensslPath) {
    Write-ColorOutput "OpenSSL not found. Please install OpenSSL first." $Red
    Write-ColorOutput "You can install it using:" $Yellow
    Write-ColorOutput "1. Chocolatey: choco install openssl" $Yellow
    Write-ColorOutput "2. Or download from: https://slproweb.com/products/Win32OpenSSL.html" $Yellow
    Write-ColorOutput "" $Yellow
    Write-ColorOutput "Alternatively, you can use mkcert for development:" $Yellow
    Write-ColorOutput "1. Install mkcert: choco install mkcert" $Yellow
    Write-ColorOutput "2. Run: mkcert -install" $Yellow
    Write-ColorOutput "3. Run: mkcert localhost 127.0.0.1 ::1" $Yellow
    exit 1
}

Write-ColorOutput "Using OpenSSL at: $opensslPath" $Yellow

# Generate development certificates
Write-ColorOutput "Generating development certificates..." $Yellow

Set-Location $sslDevPath

# Generate private key
Write-ColorOutput "Generating private key..." $Yellow
& $opensslPath genrsa -out localhost.key 2048

# Generate certificate signing request
Write-ColorOutput "Generating certificate signing request..." $Yellow
& $opensslPath req -new -key localhost.key -out localhost.csr -subj "/C=US/ST=State/L=City/O=Octoplant/CN=localhost"

# Generate self-signed certificate
Write-ColorOutput "Generating self-signed certificate..." $Yellow
& $opensslPath x509 -req -days 365 -in localhost.csr -signkey localhost.key -out localhost.crt

# Clean up CSR file
Remove-Item localhost.csr -ErrorAction SilentlyContinue

Write-ColorOutput "Development certificates generated successfully!" $Green
Write-ColorOutput "Files created:" $Yellow
Write-ColorOutput "  - ssl\dev\localhost.key (private key)" $Yellow
Write-ColorOutput "  - ssl\dev\localhost.crt (certificate)" $Yellow

# Generate production certificate template
Write-ColorOutput "Creating production certificate template..." $Yellow

Set-Location ..\prod

# Create a template for production certificates
$readmeContent = @"
# Production SSL Certificates

This directory should contain your production SSL certificates.

## Required Files:
- `localhost.crt` - SSL certificate
- `localhost.key` - Private key

## How to obtain production certificates:

### Option 1: Let's Encrypt (Free)
```bash
# Install certbot
sudo apt-get install certbot

# Generate certificate
sudo certbot certonly --standalone -d yourdomain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ./localhost.crt
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ./localhost.key
```

### Option 2: Purchase from CA
1. Generate CSR: `openssl req -new -newkey rsa:2048 -keyout localhost.key -out localhost.csr`
2. Submit CSR to your certificate authority
3. Download and save the certificate as `localhost.crt`

## Security Notes:
- Keep private keys secure and never commit them to version control
- Set proper file permissions: `chmod 600 localhost.key`
- Regularly renew certificates before expiration
"@

$readmeContent | Out-File -FilePath "README.md" -Encoding UTF8

Write-ColorOutput "Production certificate template created!" $Green

# Return to original directory
Set-Location ..\..

Write-ColorOutput "SSL certificate setup complete!" $Green
Write-ColorOutput "" $Yellow
Write-ColorOutput "Next steps:" $Yellow
Write-ColorOutput "1. For development: Certificates are ready to use" $Yellow
Write-ColorOutput "2. For production: Follow the instructions in ssl\prod\README.md" $Yellow
Write-ColorOutput "3. Update your configuration files as per the migration guide" $Yellow
Write-ColorOutput "" $Yellow
Write-ColorOutput "To test HTTPS in development:" $Yellow
Write-ColorOutput "npm run dev:https" $Yellow
Write-ColorOutput "" $Yellow
Write-ColorOutput "To test Express server with HTTPS:" $Yellow
Write-ColorOutput "npm run proxy:https" $Yellow 