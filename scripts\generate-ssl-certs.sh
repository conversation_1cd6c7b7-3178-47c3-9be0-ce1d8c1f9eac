#!/bin/bash

# SSL Certificate Generation Script for Development
# This script generates self-signed SSL certificates for local development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Generating SSL certificates for development...${NC}"

# Create directories
mkdir -p ssl/dev
mkdir -p ssl/prod

# Check if OpenSSL is installed
if ! command -v openssl &> /dev/null; then
    echo -e "${RED}OpenSSL is not installed. Please install OpenSSL first.${NC}"
    echo "On Windows with Chocolatey: choco install openssl"
    echo "On macOS with Homebrew: brew install openssl"
    echo "On Ubuntu/Debian: sudo apt-get install openssl"
    exit 1
fi

# Generate development certificates
echo -e "${YELLOW}Generating development certificates...${NC}"

cd ssl/dev

# Generate private key
echo "Generating private key..."
openssl genrsa -out localhost.key 2048

# Generate certificate signing request
echo "Generating certificate signing request..."
openssl req -new -key localhost.key -out localhost.csr -subj "/C=US/ST=State/L=City/O=Octoplant/CN=localhost"

# Generate self-signed certificate
echo "Generating self-signed certificate..."
openssl x509 -req -days 365 -in localhost.csr -signkey localhost.key -out localhost.crt

# Clean up CSR file
rm localhost.csr

# Set proper permissions
chmod 600 localhost.key
chmod 644 localhost.crt

echo -e "${GREEN}Development certificates generated successfully!${NC}"
echo "Files created:"
echo "  - ssl/dev/localhost.key (private key)"
echo "  - ssl/dev/localhost.crt (certificate)"

# Generate production certificate template
echo -e "${YELLOW}Creating production certificate template...${NC}"

cd ../prod

# Create a template for production certificates
cat > README.md << 'EOF'
# Production SSL Certificates

This directory should contain your production SSL certificates.

## Required Files:
- `localhost.crt` - SSL certificate
- `localhost.key` - Private key

## How to obtain production certificates:

### Option 1: Let's Encrypt (Free)
```bash
# Install certbot
sudo apt-get install certbot

# Generate certificate
sudo certbot certonly --standalone -d yourdomain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ./localhost.crt
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ./localhost.key
```

### Option 2: Purchase from CA
1. Generate CSR: `openssl req -new -newkey rsa:2048 -keyout localhost.key -out localhost.csr`
2. Submit CSR to your certificate authority
3. Download and save the certificate as `localhost.crt`

## Security Notes:
- Keep private keys secure and never commit them to version control
- Set proper file permissions: `chmod 600 localhost.key`
- Regularly renew certificates before expiration
EOF

echo -e "${GREEN}Production certificate template created!${NC}"

cd ../..

echo -e "${GREEN}SSL certificate setup complete!${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. For development: Certificates are ready to use"
echo "2. For production: Follow the instructions in ssl/prod/README.md"
echo "3. Update your configuration files as per the migration guide"
echo ""
echo -e "${YELLOW}To test HTTPS in development:${NC}"
echo "npm run dev:https"
echo ""
echo -e "${YELLOW}To test Express server with HTTPS:${NC}"
echo "npm run proxy:https" 