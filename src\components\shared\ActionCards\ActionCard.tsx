import React from 'react';

// Import light theme SVGs (for main card icons)
import shakeHandsIcon from '@/assets/svgs/light/shake-hands.svg';
import serverIcon from '@/assets/svgs/light/server.svg';
import gearIcon from '@/assets/svgs/light/gear.svg';
import informationIcon from '@/assets/svgs/light/information.svg';
import userIcon from '@/assets/svgs/light/user.svg';
import worldIcon from '@/assets/svgs/light/world.svg';
import wrenchIcon from '@/assets/svgs/light/wrench.svg';

// Import dark theme SVGs (for button icons)
import bookOpenIcon from '@/assets/svgs/dark/book-open.svg';
import addIcon from '@/assets/svgs/dark/add.svg';
import copyIcon from '@/assets/svgs/dark/copy.svg';
import downloadIcon from '@/assets/svgs/dark/download.svg';
import homeIcon from '@/assets/svgs/dark/home.svg';

// Create icon maps for dynamic access
const lightIcons: Record<string, string> = {
  'shake-hands': shakeHandsIcon,
  server: serverIcon,
  gear: gearIcon,
  information: informationIcon,
  user: userIcon,
  world: worldIcon,
  wrench: wrenchIcon,
};

const darkIcons: Record<string, string> = {
  'book-open': bookOpenIcon,
  add: addIcon,
  copy: copyIcon,
  download: downloadIcon,
  home: homeIcon,
};

interface ActionCardProps {
  iconName: string;
  title: string;
  description: React.ReactNode;
  buttonText: string;
  buttonIconName: string;
  onButtonClick: () => void;
}

export const ActionCard = React.memo(
  ({
    iconName,
    title,
    description,
    buttonText,
    buttonIconName,
    onButtonClick,
  }: ActionCardProps) => {
    // Get the appropriate icon URLs from the imported assets
    const mainIconSrc = lightIcons[iconName];
    const buttonIconSrc = darkIcons[buttonIconName];

    // Log warnings for missing icons in development
    if (process.env.NODE_ENV === 'development') {
      if (!mainIconSrc) {
        console.warn(`[ActionCard] Missing light icon for: ${iconName}`);
      }
      if (!buttonIconSrc) {
        console.warn(`[ActionCard] Missing dark icon for: ${buttonIconName}`);
      }
    }

    return (
      <article className="flex flex-col h-[400px] w-96 bg-primary text-background shadow-lg p-x-large">
        <header>
          <img src={mainIconSrc} alt={`${title} icon`} className="h-16" loading="lazy" />
          <h3 className="mt-8 text-2xl font-semibold uppercase">{title}</h3>
        </header>
        <div className="flex flex-col justify-between flex-1">
          <p className="mb-8 mt-8 text-background leading-snug">{description}</p>
          <button
            className="h-16 py-2 px-4 text-primary bg-secondary hover:bg-hover text-large flex items-center justify-center gap-2 transition-colors uppercase"
            onClick={onButtonClick}
            aria-label={buttonText}
            type="button"
          >
            <img
              src={buttonIconSrc}
              alt=""
              className="w-6 h-[19px]"
              loading="lazy"
              width="24"
              height="19"
            />
            <span className={buttonText === 'Browse Help' ? 'text-primary' : ''}>{buttonText}</span>
          </button>
        </div>
      </article>
    );
  }
);
