/**
 * Runtime Configuration Loader
 * Loads and validates configuration from window.APP_CONFIG
 */

import { AppConfig, ConfigValidationResult, ConfigLoadOptions } from './types';

// Global configuration instance
let configInstance: AppConfig | null = null;

/**
 * Default configuration values (fallbacks)
 */
const DEFAULT_CONFIG: Partial<AppConfig> = {
  API_TIMEOUT: 10000,
  EXPERIMENTAL: false,
  CACHE_ENABLED: false,
  CACHE_SIZE_MB: 50,
  CACHE_MAX_AGE: 3600000, // 1 hour
  DEBUG_MODE: false,
  FORCE_HTTP: false,
  ALLOW_HTTP: false,
};

/**
 * Validates the configuration object
 */
function validateConfig(config: any): ConfigValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  if (!config.API_URL) {
    errors.push('API_URL is required');
  } else if (typeof config.API_URL !== 'string') {
    errors.push('API_URL must be a string');
  } else if (!config.API_URL.endsWith('/api/') && !config.API_URL.endsWith('/api')) {
    warnings.push('API_URL should end with /api/ for consistency');
  }

  if (!config.OAUTH_URL) {
    errors.push('OAUTH_URL is required');
  } else if (typeof config.OAUTH_URL !== 'string') {
    errors.push('OAUTH_URL must be a string');
  }

  if (!config.STORAGE_URL) {
    errors.push('STORAGE_URL is required');
  } else if (typeof config.STORAGE_URL !== 'string') {
    errors.push('STORAGE_URL must be a string');
  }

  // Type validation for optional fields
  if (
    config.API_TIMEOUT !== undefined &&
    (typeof config.API_TIMEOUT !== 'number' || config.API_TIMEOUT <= 0)
  ) {
    errors.push('API_TIMEOUT must be a positive number');
  }

  if (config.EXPERIMENTAL !== undefined && typeof config.EXPERIMENTAL !== 'boolean') {
    errors.push('EXPERIMENTAL must be a boolean');
  }

  if (config.HOSTNAME !== undefined && typeof config.HOSTNAME !== 'string') {
    errors.push('HOSTNAME must be a string');
  }

  if (
    config.PORT !== undefined &&
    (typeof config.PORT !== 'number' || config.PORT <= 0 || config.PORT > 65535)
  ) {
    errors.push('PORT must be a valid port number (1-65535)');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Loads configuration from window.APP_CONFIG
 */
export function loadConfig(options: ConfigLoadOptions = {}): AppConfig {
  const {
    throwOnError = true,
    enableLogging = process.env.NODE_ENV !== 'production',
    fallbackConfig = {},
  } = options;

  try {
    // Check if config is already loaded
    if (configInstance) {
      return configInstance;
    }

    // Check if window.APP_CONFIG exists
    if (typeof window === 'undefined' || !window.APP_CONFIG) {
      throw new Error(
        'Runtime configuration not found. Ensure config.js is loaded before the application starts.'
      );
    }

    // Merge with defaults and fallback
    const rawConfig = { ...DEFAULT_CONFIG, ...fallbackConfig, ...window.APP_CONFIG };

    // Validate configuration
    const validation = validateConfig(rawConfig);

    if (enableLogging) {
      if (validation.warnings.length > 0) {
        console.warn('[Config] Configuration warnings:', validation.warnings);
      }
    }

    if (!validation.isValid) {
      const errorMessage = `Configuration validation failed: ${validation.errors.join(', ')}`;
      if (throwOnError) {
        throw new Error(errorMessage);
      } else {
        console.error('[Config]', errorMessage);
        // Return a minimal working config
        configInstance = {
          API_URL: 'http://localhost:5257/api/',
          OAUTH_URL: 'https://localhost:64023/',
          STORAGE_URL: 'http://localhost:5158/',
          API_TIMEOUT: 10000,
          EXPERIMENTAL: false,
        };
        return configInstance;
      }
    }

    // Store validated config
    configInstance = rawConfig as AppConfig;

    if (enableLogging) {
      console.log('[Config] Runtime configuration loaded successfully:', {
        API_URL: configInstance.API_URL,
        OAUTH_URL: configInstance.OAUTH_URL,
        STORAGE_URL: configInstance.STORAGE_URL,
        API_TIMEOUT: configInstance.API_TIMEOUT,
        EXPERIMENTAL: configInstance.EXPERIMENTAL,
        // Don't log sensitive or verbose config
      });
    }

    return configInstance;
  } catch (error) {
    const errorMessage = `Failed to load runtime configuration: ${error instanceof Error ? error.message : 'Unknown error'}`;

    if (throwOnError) {
      throw new Error(errorMessage);
    } else {
      console.error('[Config]', errorMessage);
      // Return emergency fallback config
      configInstance = {
        API_URL: 'http://localhost:5257/api/',
        OAUTH_URL: 'https://localhost:64023/',
        STORAGE_URL: 'http://localhost:5158/',
        API_TIMEOUT: 10000,
        EXPERIMENTAL: false,
      };
      return configInstance;
    }
  }
}

/**
 * Gets the current configuration (loads if not already loaded)
 */
export function getConfig(): AppConfig {
  return loadConfig();
}

/**
 * Reloads the configuration (useful for testing or dynamic updates)
 */
export function reloadConfig(options?: ConfigLoadOptions): AppConfig {
  configInstance = null;
  return loadConfig(options);
}

/**
 * Type declaration for global window object
 */
declare global {
  interface Window {
    APP_CONFIG?: Partial<AppConfig>;
  }
}
