import { Log, UserManagerSettings, WebStorageStateStore } from 'oidc-client-ts';
import { getConfig } from '@/config';

// Get runtime configuration
const config = getConfig();

// Set log level based on runtime configuration
const isProduction = config.ENVIRONMENT === 'production';
const logLevel = config.LOG_LEVEL || 'ERROR';
Log.setLogger(console);

// Map string log level to oidc-client-ts log levels
const getLogLevel = (level: string) => {
  switch (level.toUpperCase()) {
    case 'DEBUG':
      return Log.DEBUG;
    case 'INFO':
      return Log.INFO;
    case 'WARN':
      return Log.WARN;
    case 'ERROR':
      return Log.ERROR;
    default:
      return isProduction ? Log.ERROR : Log.WARN;
  }
};

Log.setLevel(getLogLevel(logLevel));

// Get hostname from runtime config or current browser location
const getHostname = () => {
  if (config.HOSTNAME) {
    return config.HOSTNAME;
  }

  // Extract hostname from current browser location as fallback
  const currentHostname = window.location.hostname;
  if (currentHostname && currentHostname !== 'localhost') {
    return currentHostname;
  }

  throw new Error('HOSTNAME must be set in runtime configuration. Check your config.js file.');
};

// API URL configuration from runtime config
const API_BASE_URL = config.API_URL;
if (!API_BASE_URL.endsWith('/api/') && !API_BASE_URL.endsWith('/api')) {
  throw new Error(
    'API_URL must end with /api/ (e.g., https://hostname:5256/api/). Please fix your config.js file.'
  );
}

// OAuth URL configuration
const getBaseOAuthUrl = () => {
  const envUrl = config.OAUTH_URL;
  return envUrl.endsWith('/') ? envUrl : `${envUrl}/`;
};
const OAUTH_API_URL = `${getBaseOAuthUrl()}v1/oauth2/`;

// Frontend URL configuration
const getFrontendUrl = () => {
  const hostname = getHostname();
  const port = config.PORT || window.location.port || '3000';
  const protocol = window.location.protocol || 'https:';
  return `${protocol}//${hostname}:${port}`;
};

// OIDC UserManager settings
export const settings: UserManagerSettings = {
  // Core settings
  authority: getBaseOAuthUrl(),
  metadata: {
    issuer: getBaseOAuthUrl(),
    authorization_endpoint: `${OAUTH_API_URL}authorize`,
    token_endpoint: `${OAUTH_API_URL}token`,
    jwks_uri: `${OAUTH_API_URL}jwks`,
    end_session_endpoint: `${OAUTH_API_URL}end_session`,
    response_types_supported: ['code'],
    subject_types_supported: ['public'],
    userinfo_endpoint: `${OAUTH_API_URL}userinfo`,
  },

  // Client configuration from runtime config
  client_id: config.OAUTH_CLIENT_ID || 'octoplant-web-client',
  client_secret: 'b2135d46-c9d8-41f1-a4a2-558cd1dd8271',
  redirect_uri: config.OAUTH_REDIRECT_URI || `${getFrontendUrl()}/signin-callback`,
  post_logout_redirect_uri: getFrontendUrl(),
  response_type: 'code',
  scope: 'roles openid',

  // Feature configuration - enhanced with automatic renewal
  loadUserInfo: true,
  userStore: new WebStorageStateStore({ store: window.localStorage }),
  monitorSession: true,

  // Enable both automatic silent renewal and custom token refresh for redundancy
  automaticSilentRenew: true,
  silentRequestTimeoutInSeconds: 10,
  accessTokenExpiringNotificationTimeInSeconds: 60,
  checkSessionIntervalInSeconds: 10,
  silent_redirect_uri: `${getFrontendUrl()}/silent-callback`,

  extraQueryParams: {
    audience: 'octoplant-web-client',
  },
};

// --- Developer Guidance ---
// IMPORTANT: VITE_API_URL must always end with /api/ (e.g., http://localhost:5256/api/)
// If you see an error about API URL, check your .env file and backend route prefix.
