// Production Configuration Template
// This file is used for customer deployments via Windows MSI installer
// Customers can modify this file post-installation without rebuilding

// Environment Configuration - Update for customer environment
const ENVIRONMENT_CONFIG = {
  HOSTNAME: '', // Auto-detect if empty, or set customer's hostname
  PROTOCOL: 'https:', // 'http:' or 'https:'
  FORCE_HTTP: false, // Set to true to force HTTP
};

// Service Port Configuration - Update for customer's backend services
const SERVICE_PORTS = {
  API: 64037,
  OAUTH: 64023,
  STORAGE: 64038,
  WEB: 8443, // Standard HTTP port for production
};

/**
 * Get protocol based on environment configuration
 */
function getProtocol() {
  if (ENVIRONMENT_CONFIG.FORCE_HTTP) {
    return 'http:';
  }
  return ENVIRONMENT_CONFIG.PROTOCOL;
}

/**
 * Build dynamic URLs based on hostname and protocol
 */
function buildUrls() {
  // Use configured hostname or auto-detect from browser
  const hostname =
    ENVIRONMENT_CONFIG.HOSTNAME ||
    (typeof window !== 'undefined' && window.location ? window.location.hostname : 'localhost');
  const protocol = getProtocol();

  return {
    API_URL: `${protocol}//${hostname}:${SERVICE_PORTS.API}/api/`,
    OAUTH_URL: `${protocol}//${hostname}:${SERVICE_PORTS.OAUTH}/`,
    STORAGE_URL: `${protocol}//${hostname}:${SERVICE_PORTS.STORAGE}/api/Files`,
    OAUTH_REDIRECT_URI: `${protocol}//${hostname}:${SERVICE_PORTS.WEB}/signin-callback`,
  };
}

// Generate dynamic URLs
const dynamicUrls = buildUrls();

window.APP_CONFIG = {
  // Core API Configuration - Dynamic URLs
  API_URL: dynamicUrls.API_URL,
  OAUTH_URL: dynamicUrls.OAUTH_URL,
  STORAGE_URL: dynamicUrls.STORAGE_URL,
  API_TIMEOUT: 15000,

  // Environment Configuration
  ENVIRONMENT: 'production',

  // Feature Flags
  EXPERIMENTAL: false,

  // Runtime Configuration
  HOSTNAME:
    ENVIRONMENT_CONFIG.HOSTNAME ||
    (typeof window !== 'undefined' && window.location ? window.location.hostname : 'localhost'),
  PORT: SERVICE_PORTS.WEB,

  // Cache Configuration
  CACHE_ENABLED: true,
  CACHE_SIZE_MB: 100,
  CACHE_MAX_AGE: 3600000,

  // Production Configuration
  DEBUG_MODE: false,
  FORCE_HTTP: ENVIRONMENT_CONFIG.FORCE_HTTP,
  ALLOW_HTTP: false, // Enforce HTTPS in production

  // Logging Configuration
  LOG_LEVEL: 'ERROR', // Minimal logging in production

  // Application Metadata
  APP_VERSION: '0.1.0',

  // OAuth Client Configuration
  OAUTH_CLIENT_ID: 'octoplant-web-client',
  OAUTH_REDIRECT_URI: dynamicUrls.OAUTH_REDIRECT_URI,

  // Additional runtime settings
  THEME: 'default',
  LANGUAGE: 'en',
};

// Production logging (minimal)
if (window.console && window.console.log) {
  console.log('[Config] Production configuration loaded');
}
