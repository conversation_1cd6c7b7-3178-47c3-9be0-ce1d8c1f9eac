import { http, HttpResponse } from 'msw';

// Mock API base URL
const API_BASE = 'https://AbdullahChishtiWeb:5257/api';

export const handlers = [
  // Auth endpoints
  http.get(`${API_BASE}/auth/user`, () => {
    return HttpResponse.json({
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'user',
    });
  }),

  // Projects endpoints
  http.get(`${API_BASE}/projects`, () => {
    return HttpResponse.json([
      {
        id: '1',
        name: 'Test Project 1',
        description: 'A test project',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '2',
        name: 'Test Project 2',
        description: 'Another test project',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ]);
  }),

  http.get(`${API_BASE}/projects/:id`, ({ params }) => {
    return HttpResponse.json({
      id: params.id,
      name: `Test Project ${params.id}`,
      description: 'A test project',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
  }),

  // Components endpoints
  http.get(`${API_BASE}/components`, () => {
    return HttpResponse.json([
      {
        id: '1',
        name: 'Test Component 1',
        type: 'feature',
        status: 'active',
        projectId: '1',
      },
      {
        id: '2',
        name: 'Test Component 2',
        type: 'core',
        status: 'inactive',
        projectId: '1',
      },
    ]);
  }),

  // Jobs endpoints
  http.get(`${API_BASE}/jobs`, () => {
    return HttpResponse.json([
      {
        id: '1',
        name: 'Test Job 1',
        status: 'completed',
        projectId: '1',
        createdAt: new Date().toISOString(),
        completedAt: new Date().toISOString(),
      },
      {
        id: '2',
        name: 'Test Job 2',
        status: 'running',
        projectId: '1',
        createdAt: new Date().toISOString(),
      },
    ]);
  }),

  // Files endpoints
  http.get(`${API_BASE}/files`, () => {
    return HttpResponse.json([
      {
        id: '1',
        name: 'test-file.txt',
        size: 1024,
        type: 'text/plain',
        uploadedAt: new Date().toISOString(),
      },
    ]);
  }),

  // Error handlers
  http.all('*', () => {
    return new HttpResponse(null, { status: 404 });
  }),
];
