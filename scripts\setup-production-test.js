#!/usr/bin/env node

/**
 * Automated Production Testing Setup
 * Configures hostname, certificates, ports, and OAuth settings for consistent production testing
 */

import { execSync } from 'child_process';
import { existsSync, readFileSync, writeFileSync } from 'fs';
import { join } from 'path';
import os from 'os';

const CONFIG_FILE = 'public/config.js';
const CERT_DIR = 'certs';

// Configuration detection and setup
class ProductionTestSetup {
  constructor() {
    this.hostname = this.detectHostname();
    this.protocol = 'https:';
    this.port = 3000;
    this.certPath = join(CERT_DIR, `${this.hostname}.pem`);
    this.keyPath = join(CERT_DIR, `${this.hostname}-key.pem`);
  }

  detectHostname() {
    // Priority: Environment variable > OS hostname
    return process.env.VITE_HOSTNAME || os.hostname();
  }

  async run() {
    console.log('🚀 Setting up automated production testing...\n');

    try {
      await this.validateEnvironment();
      await this.ensureCertificates();
      await this.killConflictingProcesses();
      await this.generateProductionConfig();
      await this.validateConfiguration();

      console.log('✅ Production testing setup complete!\n');
      this.displaySummary();
    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      console.error('Stack trace:', error.stack);
      process.exit(1);
    }
  }

  async validateEnvironment() {
    console.log('🔍 Validating environment...');

    // Check if mkcert is available for certificate generation
    try {
      execSync('mkcert -version', { stdio: 'ignore' });
      console.log('  ✅ mkcert is available');
    } catch {
      console.log('  ⚠️  mkcert not found - will use HTTP fallback');
      this.protocol = 'http:';
    }

    console.log(`  ✅ Hostname detected: ${this.hostname}`);
    console.log(`  ✅ Protocol: ${this.protocol.replace(':', '')}`);
  }

  async ensureCertificates() {
    if (this.protocol === 'http:') {
      console.log('📝 Using HTTP - skipping certificate setup');
      return;
    }

    console.log('🔐 Ensuring HTTPS certificates...');

    if (existsSync(this.certPath) && existsSync(this.keyPath)) {
      console.log('  ✅ Certificates already exist');
      return;
    }

    try {
      console.log(`  🔧 Generating certificates for ${this.hostname}...`);
      execSync(`mkcert -install`, { stdio: 'inherit' });
      execSync(
        `mkcert -cert-file "${this.certPath}" -key-file "${this.keyPath}" "${this.hostname}" localhost`,
        { stdio: 'inherit' }
      );
      console.log('  ✅ Certificates generated successfully');
    } catch (error) {
      console.log('  ⚠️  Certificate generation failed - falling back to HTTP');
      this.protocol = 'http:';
    }
  }

  async killConflictingProcesses() {
    console.log('🔄 Checking for port conflicts...');

    // Skip automatic process killing to avoid issues
    // Users can manually stop processes if needed
    console.log('  ℹ️  Skipping automatic process termination');
    console.log('  💡 If you encounter port conflicts, manually stop other servers');
    console.log('  ✅ Port check completed');
  }

  async generateProductionConfig() {
    console.log('⚙️  Generating production configuration...');

    const config = this.buildConfigContent();
    writeFileSync(CONFIG_FILE, config);

    console.log('  ✅ Production config.js generated');
    console.log(
      `  📍 OAuth Redirect URI: ${this.protocol}//${this.hostname}:${this.port}/signin-callback`
    );
  }

  buildConfigContent() {
    return `// Auto-generated Production Testing Configuration
// Generated on: ${new Date().toISOString()}
// Hostname: ${this.hostname}
// Protocol: ${this.protocol.replace(':', '')}

// Environment Configuration - Auto-detected
const ENVIRONMENT_CONFIG = {
  HOSTNAME: '${this.hostname}',
  PROTOCOL: '${this.protocol}',
  FORCE_HTTP: ${this.protocol === 'http:'},
};

// Service Port Configuration - Production Testing
const SERVICE_PORTS = {
  API: 5257,
  OAUTH: 64023,
  STORAGE: 5158,
  WEB: ${this.port},
};

/**
 * Get protocol based on environment configuration
 */
function getProtocol() {
  if (ENVIRONMENT_CONFIG.FORCE_HTTP) {
    return 'http:';
  }
  return ENVIRONMENT_CONFIG.PROTOCOL;
}

/**
 * Build dynamic URLs based on hostname and protocol
 */
function buildUrls() {
  const hostname = ENVIRONMENT_CONFIG.HOSTNAME;
  const protocol = getProtocol();

  return {
    API_URL: \`\${protocol}//\${hostname}:\${SERVICE_PORTS.API}/api/\`,
    OAUTH_URL: \`\${protocol}//\${hostname}:\${SERVICE_PORTS.OAUTH}/\`,
    STORAGE_URL: \`\${protocol}//\${hostname}:\${SERVICE_PORTS.STORAGE}/api/Files\`,
    OAUTH_REDIRECT_URI: \`\${protocol}//\${hostname}:\${SERVICE_PORTS.WEB}/signin-callback\`,
  };
}

// Generate dynamic URLs
const dynamicUrls = buildUrls();

window.APP_CONFIG = {
  // Core API Configuration - Dynamic URLs
  API_URL: dynamicUrls.API_URL,
  OAUTH_URL: dynamicUrls.OAUTH_URL,
  STORAGE_URL: dynamicUrls.STORAGE_URL,
  API_TIMEOUT: 10000,

  // Environment Configuration
  ENVIRONMENT: 'production-test',

  // Feature Flags
  EXPERIMENTAL: false,

  // Runtime Configuration
  HOSTNAME: ENVIRONMENT_CONFIG.HOSTNAME,
  PORT: SERVICE_PORTS.WEB,

  // Cache Configuration
  CACHE_ENABLED: true,
  CACHE_SIZE_MB: 50,
  CACHE_MAX_AGE: 3600000,

  // Development/Debug Configuration
  DEBUG_MODE: false,
  FORCE_HTTP: ENVIRONMENT_CONFIG.FORCE_HTTP,
  ALLOW_HTTP: true,

  // Logging Configuration
  LOG_LEVEL: 'WARN',

  // Application Metadata
  APP_VERSION: '0.1.0',

  // OAuth Client Configuration
  OAUTH_CLIENT_ID: 'octoplant-web-client',
  OAUTH_REDIRECT_URI: dynamicUrls.OAUTH_REDIRECT_URI,

  // Additional runtime settings
  THEME: 'default',
  LANGUAGE: 'en',
};

// Configuration logging
if (window.console && window.console.log) {
  console.log('[Config] 🚀 Production testing configuration loaded:', {
    hostname: ENVIRONMENT_CONFIG.HOSTNAME,
    protocol: getProtocol(),
    oauthRedirectUri: dynamicUrls.OAUTH_REDIRECT_URI,
    environment: 'production-test'
  });
}`;
  }

  async validateConfiguration() {
    console.log('✅ Validating configuration...');

    if (!existsSync(CONFIG_FILE)) {
      throw new Error('Configuration file was not created');
    }

    const config = readFileSync(CONFIG_FILE, 'utf8');
    if (!config.includes(this.hostname)) {
      throw new Error('Configuration does not contain expected hostname');
    }

    console.log('  ✅ Configuration file validated');
  }

  displaySummary() {
    console.log('📋 Production Testing Summary:');
    console.log(`  🌐 Access URL: ${this.protocol}//${this.hostname}:${this.port}`);
    console.log(`  🔐 Protocol: ${this.protocol.replace(':', '').toUpperCase()}`);
    console.log(
      `  🔑 OAuth Redirect: ${this.protocol}//${this.hostname}:${this.port}/signin-callback`
    );
    console.log(
      `  📁 Certificates: ${this.protocol === 'https:' ? 'Generated' : 'Not needed (HTTP)'}`
    );
    console.log('\n🚀 Ready to run: npm run test:prod:auto');
  }
}

// Run the setup
const setup = new ProductionTestSetup();
setup.run().catch(console.error);
