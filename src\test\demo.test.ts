import { describe, it, expect } from 'vitest';

// Simple utility function for demo purposes
const add = (a: number, b: number): number => {
  return a + b;
};

const formatName = (firstName: string, lastName: string): string => {
  return `${firstName} ${lastName}`.trim();
};

describe('Demo Tests', () => {
  describe('add function', () => {
    it('should add two positive numbers correctly', () => {
      expect(add(2, 3)).toBe(5);
      expect(add(0, 0)).toBe(0);
      expect(add(-1, 1)).toBe(0);
    });

    it('should handle negative numbers', () => {
      expect(add(-2, -3)).toBe(-5);
      expect(add(-5, 3)).toBe(-2);
    });
  });

  describe('formatName function', () => {
    it('should format name correctly', () => {
      expect(formatName('John', 'Doe')).toBe('<PERSON>');
      expect(formatName('<PERSON>', '<PERSON>')).toBe('<PERSON>');
    });

    it('should handle empty strings', () => {
      expect(formatName('', 'Doe')).toBe('Doe');
      expect(formatName('<PERSON>', '')).toBe('John');
      expect(formatName('', '')).toBe('');
    });
  });
});
