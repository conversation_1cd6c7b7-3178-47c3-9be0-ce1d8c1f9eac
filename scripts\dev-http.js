#!/usr/bin/env node

/**
 * Start development server in HTTP mode
 */

import { spawn } from 'child_process';

console.log('🌐 Starting development server in HTTP mode...');

// Set environment variable and start dev server
process.env.VITE_FORCE_HTTP = 'true';

const devServer = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  shell: process.platform === 'win32',
  env: process.env,
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down development server...');
  devServer.kill('SIGINT');
  process.exit(0);
});

devServer.on('close', code => {
  process.exit(code);
});
