#!/usr/bin/env node

/**
 * Intelligent Production Serve <PERSON>
 * Automatically serves the production build with the correct protocol and certificates
 */

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';
import os from 'os';

class AutoServe {
  constructor() {
    this.hostname = process.env.VITE_HOSTNAME || os.hostname();
    this.port = 3000;
    this.certPath = join('certs', `${this.hostname}.pem`);
    this.keyPath = join('certs', `${this.hostname}-key.pem`);
    this.useHttps = existsSync(this.certPath) && existsSync(this.keyPath);
  }

  async serve() {
    const args = [
      'serve',
      'dist',
      '-s', // Single page application mode
      '-l',
      this.port.toString(),
    ];

    if (this.useHttps) {
      args.push('--ssl-cert', this.certPath);
      args.push('--ssl-key', this.keyPath);
      console.log(`🔐 Starting HTTPS server on https://${this.hostname}:${this.port}`);
    } else {
      console.log(`🌐 Starting HTTP server on http://${this.hostname}:${this.port}`);
      console.log('⚠️  HTTPS certificates not found - using HTTP');
    }

    console.log('📁 Serving from: dist/');
    console.log('🔄 Starting server...\n');

    // Spawn the serve process
    const serveProcess = spawn('npx', args, {
      stdio: 'inherit',
      shell: true,
    });

    // Handle process events
    serveProcess.on('error', error => {
      console.error('❌ Failed to start server:', error.message);
      process.exit(1);
    });

    serveProcess.on('close', code => {
      if (code !== 0) {
        console.error(`❌ Server exited with code ${code}`);
        process.exit(code);
      }
    });

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down server...');
      serveProcess.kill('SIGINT');
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 Shutting down server...');
      serveProcess.kill('SIGTERM');
    });
  }
}

// Run the auto-serve
const autoServe = new AutoServe();
autoServe.serve().catch(console.error);
