#!/usr/bin/env node

/**
 * Quick HTTPS setup script (without starting the server)
 * 1. Shows hostname
 * 2. Generates certificates for the hostname
 * 3. Updates .env configuration
 * 4. Provides next steps
 */

import { execSync } from 'child_process';
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs';
import os from 'os';
import path from 'path';

const hostname = os.hostname();
const certsDir = './certs';
const envFile = './.env';

console.log('⚡ Quick HTTPS Setup');
console.log('='.repeat(30));

// Check if dependencies are installed
if (!existsSync('./node_modules')) {
  console.error('❌ Dependencies not installed!');
  console.error('📋 Please run this first:');
  console.error('   npm install');
  console.error('');
  console.error('💡 Then run the setup again:');
  console.error('   npm run quick-setup');
  process.exit(1);
}

// Step 1: Show hostname
console.log('🔍 Detecting hostname...');
console.log(`✅ Your hostname is: ${hostname}`);

// Step 2: Ensure certs directory exists
console.log('\n📁 Setting up certificates directory...');
if (!existsSync(certsDir)) {
  mkdirSync(certsDir, { recursive: true });
  console.log('✅ Created certs directory');
} else {
  console.log('✅ Certs directory already exists');
}

// Step 3: Check if certificates already exist
const certFile = path.join(certsDir, `${hostname}.pem`);
const keyFile = path.join(certsDir, `${hostname}-key.pem`);

console.log('\n🔐 Checking for existing certificates...');
if (existsSync(certFile) && existsSync(keyFile)) {
  console.log('✅ Certificates already exist for this hostname');
} else {
  console.log('📝 Generating certificates...');

  // Check if mkcert is available
  try {
    execSync('mkcert -version', { stdio: 'ignore' });
  } catch (error) {
    console.error('❌ mkcert is not installed or not in PATH');
    console.error('📋 To install mkcert:');
    console.error('   • Windows: choco install mkcert');
    console.error('   • macOS: brew install mkcert');
    console.error('   • Linux: See https://github.com/FiloSottile/mkcert#installation');
    process.exit(1);
  }

  // Install mkcert CA and generate certificates
  try {
    console.log('🔧 Installing mkcert CA...');
    execSync('mkcert -install', { stdio: 'pipe' });

    console.log(`🔑 Generating certificates for ${hostname}...`);
    const originalCwd = process.cwd();
    process.chdir(certsDir);
    execSync(`mkcert ${hostname}`, { stdio: 'pipe' });
    process.chdir(originalCwd);

    console.log('✅ Certificates generated successfully!');
  } catch (error) {
    console.error('❌ Failed to generate certificates:', error.message);
    process.exit(1);
  }
}

// Step 4: Update .env file
console.log('\n⚙️  Updating environment configuration...');
if (existsSync(envFile)) {
  let envContent = readFileSync(envFile, 'utf8');

  // Update or add VITE_HOSTNAME
  if (envContent.includes('VITE_HOSTNAME=')) {
    envContent = envContent.replace(/VITE_HOSTNAME=.*/g, `VITE_HOSTNAME=${hostname}`);
    console.log('✅ Updated VITE_HOSTNAME in .env');
  } else {
    envContent = `VITE_HOSTNAME=${hostname}\n` + envContent;
    console.log('✅ Added VITE_HOSTNAME to .env');
  }

  // Ensure HTTP flags are commented out for HTTPS mode
  envContent = envContent.replace(/^VITE_FORCE_HTTP=true/gm, '# VITE_FORCE_HTTP=true');
  envContent = envContent.replace(/^VITE_ALLOW_HTTP=true/gm, '# VITE_ALLOW_HTTP=true');

  writeFileSync(envFile, envContent);
  console.log('✅ Configured .env for HTTPS mode');
} else {
  console.log('⚠️  .env file not found, creating basic configuration...');
  const basicEnv = `VITE_HOSTNAME=${hostname}
PORT=3000

# HTTP/HTTPS Configuration for development server
# VITE_FORCE_HTTP=true          # Force HTTP mode (skip certificate search)
# VITE_ALLOW_HTTP=true          # Allow HTTP fallback if no certificates found

# Note: Backend URLs are now configured in public/config.js
# Run 'npm run config development' to set up application configuration
`;
  writeFileSync(envFile, basicEnv);
  console.log('✅ Created .env file with HTTPS configuration');
  console.log(
    '💡 Remember to run "npm run config development" to set up application configuration'
  );
}

// Step 5: Show summary and next steps
console.log('\n🎉 HTTPS Setup Complete!');
console.log('='.repeat(25));
console.log(`🌐 Hostname: ${hostname}`);
console.log(`🔒 Certificate: ${certFile}`);
console.log(`🔑 Private Key: ${keyFile}`);
console.log(`🚀 Frontend URL: https://${hostname}:3000`);

console.log('\n📋 Next Steps:');
console.log('1. Start development server: npm run dev');
console.log('2. Or use the all-in-one command: npm run setup-https');
console.log(`3. Open your browser to: https://${hostname}:3000`);

console.log('\n💡 Useful Commands:');
console.log('• npm run dev          - Start development server');
console.log('• npm run setup-https  - Complete setup + start server');
console.log('• npm run hostname     - Show current hostname');
console.log('• npm run generate-certs - Generate certificates only');

console.log('\n✅ Ready for HTTPS development!');
