import { t } from 'i18next';
import logoIcon from '@/assets/svgs/dark/Octoplant.svg';
import Dialog from '../shared/Dialog/Dialog';
import VersionDisplay from '../VersionDisplay/VersionDisplay';
import { getVersionDisplayName, SERVER_VERSION } from '@/utils/version';

interface AboutDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AboutDialog: React.FC<AboutDialogProps> = ({ isOpen, onClose }) => {
  const title = t('common.about') + ' ' + t('common.serverProduct');
  const subTitle = getVersionDisplayName(SERVER_VERSION);
  const amdtUrl = t('about.url');

  return (
    <Dialog isOpen={isOpen} onClose={onClose} title={title} subtitle={subTitle}>
      <div>
        <img className="w-36" src={logoIcon} />
        <p className="pt-4">
          {t('common.productName')}
          <br />
          <VersionDisplay />
          <br />
          &nbsp;
          <br />
          {t('about.copyright')}
          <br />
          {t('about.address')}
          <br />
          <a href={amdtUrl} target="_blank" rel="noreferrer">
            {t('about.displayUrl')}
          </a>
        </p>
      </div>
    </Dialog>
  );
};
