import express from 'express';
import proxy from 'express-http-proxy';
import path from 'path';
import { fileURLToPath } from 'url';
import helmet from 'helmet'; // For security headers
import dotenv from 'dotenv';

dotenv.config();

// Necessary to handle ES module __dirname and __filename
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// Add CSP header explicitly including connect-src
app.use((req, res, next) => {
    helmet.contentSecurityPolicy({
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'"],
            styleSrc: ["'self'", 'https:'],
            imgSrc: ["'self'", 'data:'],
            connectSrc: ["'self'", 'https://localhost:64023', 'https://webclients-dev:5156'],
            fontSrc: ["'self'", 'https:'],
            objectSrc: ["'none'"],
            upgradeInsecureRequests: []
        }
    })(req, res, next);
});

// Add a middleware to log response headers for debugging
app.use((req, res, next) => {
    res.on('finish', () => {
        console.log('Response headers:', res.getHeaders());
    });
    next();
});

app.use('/insecureapiproxyforcors', proxy(process.env.BACKEND_SERVER_URL || 'https://localhost:64023', {
    proxyReqOptDecorator: function(proxyReqOpts, srcReq) {
        proxyReqOpts.rejectUnauthorized = false; // Allow self-signed certificates
        return proxyReqOpts;
    },
    proxyReqPathResolver: function(req) {
        return req.originalUrl.replace('/insecureapiproxyforcors', '');
    },
    userResDecorator: function(proxyRes, proxyResData, userReq, userRes) {
        const data = JSON.parse(proxyResData.toString('utf8'));
        data["id_token"] = data["access_token"];
        return JSON.stringify(data);
    }
}));

app.use((req, res, next) => {
    res.setHeader('Cross-Origin-Opener-Policy', 'unsafe-none');
    res.setHeader('Cross-Origin-Embedder-Policy', 'unsafe-none');
    res.setHeader('Access-Control-Allow-Origin', '*');
    next();
});

app.use(express.static(path.join(__dirname, 'build')));

app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).send('Something went wrong!');
});

const port = process.env.PORT || 5001; // Change the port here
app.listen(port, () => {
    console.log(`Server is running on port ${port}`);
});
