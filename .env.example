# Frontend Configuration
# REQUIRED: The hostname for your development environment
# Run `npm run hostname` to find your machine's hostname
# Use your machine's actual hostname, not localhost
VITE_HOSTNAME=YourMachineName

# The port for the Vite dev server (will auto-increment if in use)
PORT=3000

# Backend Services
# Complete URLs for backend services (recommended approach)
# Replace YourMachineName with your actual hostname
VITE_API_URL=https://YourMachineName:5257/api/
VITE_OAUTH_URL=https://YourMachineName:64023/
VITE_STORAGE_URL=https://YourMachineName:5158/Files

# Alternative: Individual Configuration
# If you don't provide full URLs above, these will be used to construct them
# VITE_API_PORT=5257
# VITE_OAUTH_PORT=64023
# VITE_API_PROTOCOL=https

# SSL Certificate Configuration (Optional)
# If not specified, will auto-detect based on hostname
# VITE_SSL_KEY_PATH=./certs/custom-key.pem
# VITE_SSL_CERT_PATH=./certs/custom.pem

# HTTP/HTTPS Configuration
# VITE_FORCE_HTTP=true          # Force HTTP mode (skip certificate search)
# VITE_ALLOW_HTTP=true          # Allow HTTP fallback if no certificates found

# Other Settings
VITE_API_TIMEOUT=10000
VITE_EXPERIMENTAL=false

# Instructions:
# 1. Copy this file to .env
# 2. REQUIRED: Set VITE_HOSTNAME to your machine's actual hostname (not localhost)
#    - Windows: Run `hostname` in Command Prompt or `npm run hostname`
#    - Mac/Linux: Run `hostname` in Terminal or `npm run hostname`
# 3. Generate SSL certificates: `npm run generate-certs`
# 4. Update the backend URLs to match your server configuration
# 5. The application will automatically construct redirect URIs based on your hostname and port
# 6. If VITE_HOSTNAME is not set, the app will throw an error with instructions
