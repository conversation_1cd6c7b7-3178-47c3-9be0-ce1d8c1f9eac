import React from 'react';
import ChangeHistory from './ChangeHistory/ChangeHistory';
import JobList from './JobList/JobList';
import ComponentDetails from './ComponentDetails/ComponentDetails';
import { VersionDetailsProps, VersionDetailsCurrentPage } from './types';
import { useTreeContext } from '@/context/TreeContext';
import TabBar from '../shared/TabBar/TabBar';
import { CompareResultIconToolTips } from '../shared/CompareResultIcon/CompareResultIcon';

const VersionDetails: React.FC<VersionDetailsProps> = ({ details }) => {
  const { currentDetailsPage } = useTreeContext();

  let content;
  switch (currentDetailsPage) {
    case VersionDetailsCurrentPage.JobList:
      content = <JobList details={details} />;
      break;
    case VersionDetailsCurrentPage.Details:
      content = <ComponentDetails details={details} />;
      break;
    default:
      content =
        details && details.id ? (
          <ChangeHistory componentId={details.id} />
        ) : (
          <ChangeHistory componentId={details.componentId || details.objectId || ''} />
        );
  }

  return (
    <div className="mt-4">
      <TabBar
        tabs={[
          { page: VersionDetailsCurrentPage.ChangeHistory },
          { page: VersionDetailsCurrentPage.JobList },
          { page: VersionDetailsCurrentPage.Details },
        ]}
      />
      {content}
      <CompareResultIconToolTips />
    </div>
  );
};

export default VersionDetails;
