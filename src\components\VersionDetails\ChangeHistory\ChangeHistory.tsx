import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { useTranslation } from 'react-i18next';

import { ChangeHistoryProps } from '../types';
import { Version } from '@/utils/types';
import { DateToString } from '@/utils/dateTime';
import { useVersionDetails } from '@/hooks/queries/useVersionDetails';
import { useVersionFileDownload } from '@/hooks/queries/useVersionFileDownload';

import { GenericTable, HeaderCell } from '@/components/shared/GenericTable/GenericTable';
import ChangeHistoryDetails from './ChangeHistoryDetails/ChangeHistoryDetails';
import ToolButton, { ToolButtonStyle } from '@/components/shared/ToolButton/ToolButton';
import { PagingButtons } from '@/components/shared/PagingButtons/PagingButtons';
import { LoadingSpinner } from '@/components/shared/LoadingSpinner/LoadingSpinner';

// Import SVG assets
import downloadWhiteSvg from '@/assets/svgs/light/download.svg';
import downloadSvg from '@/assets/svgs/dark/download.svg';
import timestampSvg from '@/assets/svgs/dark/timestamp.svg';
import usernameSvg from '@/assets/svgs/dark/user.svg';
import changesSvg from '@/assets/svgs/dark/changes.svg';
import versionSvg from '@/assets/svgs/dark/version.svg';
// Download button cell component
const DownloadCell: React.FC<{ version: Version }> = React.memo(({ version }) => {
  const { mutate: downloadFile, isPending: isDownloading } = useVersionFileDownload();

  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation();
    downloadFile({
      componentId: version.componentId,
      version: String(version.version),
      filename: `version-${version.version}.zip`,
    });
  };

  return (
    <div className="flex gap-medium">
      {isDownloading ? (
        <div className="flex items-center justify-center w-8 h-8">
          <LoadingSpinner />
        </div>
      ) : (
        <ToolButton
          type={ToolButtonStyle.PrimaryBlue}
          icon={downloadWhiteSvg}
          onClick={handleDownload}
        />
      )}
    </div>
  );
});

const ChangeHistory: React.FC<ChangeHistoryProps> = ({ componentId }) => {
  // Pagination state
  const [pageNumber, setPageNumber] = useState(1);
  const pageSize = 20;

  // Safety check - ensure valid component ID
  const validComponentId = componentId && typeof componentId === 'string' ? componentId : '';
  const isEnabled = validComponentId.trim().length > 0;

  // Reset page when component changes
  useEffect(() => {
    setPageNumber(1);
  }, [validComponentId]);

  // Fetch version details with pagination
  const { data, isLoading } = useVersionDetails(validComponentId, pageNumber, pageSize, {
    enabled: isEnabled,
  });

  // Extract versions and pagination info
  const versions = data?.versions || [];
  const totalPages = data?.pageInfo
    ? Math.ceil(data.pageInfo.totalRecords / data.pageInfo.pageSize)
    : 0;
  const { t } = useTranslation();

  const renderDateContent = useCallback(
    (value: any) => (value ? DateToString(value) : t('common.notAvailable')),
    [t]
  );

  const renderCellContent = useCallback((value: any) => value || t('common.notAvailable'), [t]);

  const columns = useMemo<ColumnDef<Version>[]>(
    () => [
      {
        header: () => <HeaderCell title={t('version.version')} icon={versionSvg} />,
        accessorKey: 'version',
        cell: ({ row }) => (
          <span>
            {renderCellContent(row.original.version)}{' '}
            {row.original.versionIdentifier
              ? '- [' + row.original.versionIdentifier + ']'
              : '\u0a00'}
          </span>
        ),
      },
      {
        header: () => <HeaderCell title={t('version.timestampLocal')} icon={timestampSvg} />,
        accessorKey: 'timestamp',
        cell: ({ getValue }) => renderDateContent(getValue()),
      },
      {
        header: () => <HeaderCell title={t('version.username')} icon={usernameSvg} />,
        accessorKey: 'username',
        cell: ({ getValue }) => renderCellContent(getValue()),
      },
      {
        header: () => <HeaderCell title={t('version.changes')} icon={changesSvg} />,
        accessorKey: 'changeReason',
        cell: ({ getValue }) => renderCellContent(getValue()),
      },
      {
        header: () => <HeaderCell title={t('version.download')} icon={downloadSvg} />,
        id: 'actions',
        cell: ({ row }) => <DownloadCell version={row.original} />,
      },
    ],
    [t, renderCellContent, renderDateContent]
  );

  return (
    <div className="mt-10">
      <GenericTable
        data={versions}
        columns={columns}
        renderSubComponent={(version: Version) => <ChangeHistoryDetails version={version} />}
        getRowCanExpand={() => true}
        highlightCurrentRow={true}
      />
      {isLoading ? (
        <div className="flex justify-center my-8">
          <LoadingSpinner />
        </div>
      ) : (
        <PagingButtons
          buttons={3}
          currentPage={pageNumber}
          pages={totalPages}
          clickHandler={setPageNumber}
        />
      )}
    </div>
  );
};

export default ChangeHistory;
