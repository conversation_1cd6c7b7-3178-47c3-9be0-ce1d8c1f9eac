/**
 * Version utilities for the Octoplant Web Application
 */

import { getConfig } from '@/config';

// Get version from runtime configuration
const config = getConfig();
export const APP_VERSION = config.APP_VERSION || '0.1.0';
export const SERVER_VERSION = '105.0.0';

// Parse semantic version
export interface SemanticVersion {
  major: number;
  minor: number;
  patch: number;
  prerelease?: string;
  build?: string;
}

export function parseVersion(version: string): SemanticVersion {
  const regex = /^(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9.-]+))?(?:\+([a-zA-Z0-9.-]+))?$/;
  const match = version.match(regex);

  if (!match) {
    throw new Error(`Invalid semantic version: ${version}`);
  }

  return {
    major: parseInt(match[1], 10),
    minor: parseInt(match[2], 10),
    patch: parseInt(match[3], 10),
    prerelease: match[4],
    build: match[5],
  };
}

export function formatVersion(version: SemanticVersion): string {
  let result = `${version.major}.${version.minor}.${version.patch}`;

  if (version.prerelease) {
    result += `-${version.prerelease}`;
  }

  if (version.build) {
    result += `+${version.build}`;
  }

  return result;
}

export function compareVersions(a: string, b: string): number {
  const versionA = parseVersion(a);
  const versionB = parseVersion(b);

  // Compare major
  if (versionA.major !== versionB.major) {
    return versionA.major - versionB.major;
  }

  // Compare minor
  if (versionA.minor !== versionB.minor) {
    return versionA.minor - versionB.minor;
  }

  // Compare patch
  if (versionA.patch !== versionB.patch) {
    return versionA.patch - versionB.patch;
  }

  // Compare prerelease (no prerelease is greater than prerelease)
  if (!versionA.prerelease && versionB.prerelease) return 1;
  if (versionA.prerelease && !versionB.prerelease) return -1;
  if (versionA.prerelease && versionB.prerelease) {
    return versionA.prerelease.localeCompare(versionB.prerelease);
  }

  return 0;
}

export function isNewerVersion(current: string, target: string): boolean {
  return compareVersions(target, current) > 0;
}

// Version display helpers
export function getVersionDisplayName(version: string): string {
  const parsed = parseVersion(version);

  if (parsed.prerelease) {
    if (parsed.prerelease.startsWith('dev')) {
      return `v${version} (Development)`;
    }
    if (parsed.prerelease.startsWith('rc')) {
      return `v${version} (Release Candidate)`;
    }
    if (parsed.prerelease.startsWith('beta')) {
      return `v${version} (Beta)`;
    }
    if (parsed.prerelease.startsWith('alpha')) {
      return `v${version} (Alpha)`;
    }
  }

  return `v${version}`;
}

export function getVersionBadgeColor(version: string): string {
  const parsed = parseVersion(version);

  if (parsed.prerelease) {
    if (parsed.prerelease.startsWith('dev')) return 'bg-orange-500';
    if (parsed.prerelease.startsWith('rc')) return 'bg-blue-500';
    if (parsed.prerelease.startsWith('beta')) return 'bg-yellow-500';
    if (parsed.prerelease.startsWith('alpha')) return 'bg-red-500';
  }

  return 'bg-green-500';
}
