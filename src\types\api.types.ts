// src/types/api.types.ts
// Contains all API response interfaces and related types

/**
 * Response structure for job API calls
 */
export interface JobApiResponse {
  jobs: Array<{
    id: string;
    name: string;
    componentId: string;
    lastExecution?: string;
    nextExecution?: string;
    isDisabled: boolean;
    executionState: string;
  }>;
}

/**
 * Response structure for job results API calls
 */
export interface JobResultApiResponse {
  results: Array<{
    id: string;
    jobId: string;
    timestamp: string;
    status: string;
    message?: string;
    data?: any;
  }>;
  pageInfo: {
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  };
}

/**
 * Response structure for job status API calls
 */
export interface JobStatusResponse {
  id: string;
  status: string;
  timestamp: string;
}

/**
 * Response structure for tree node API calls
 */
export interface ApiTreeResponse {
  nodes: Array<{
    id: string;
    objectId: string;
    name: string;
    parentId: string;
    nodeType: string;
    icon?: string;
    path?: string;
  }>;
  pageInfo: {
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  };
}

/**
 * Response structure for version API calls
 */
export interface VersionApiResponse {
  versions: Array<{
    componentId?: string;
    username?: string;
    version?: string;
    versionIdentifier?: string;
    changeReason?: string;
    comment?: string;
    checksum?: string;
    downloadLink?: string;
    timestamp?: string;
  }>;
  pageInfo: {
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  };
}

/**
 * Response structure for root ID API call
 */
export interface RootIdResponse {
  id: string;
  name?: string;
  nodeType?: string;
  objectId?: string;
  parentId?: string;
  path?: string;
  // Add index signature to allow dynamic property access
  [key: string]: string | undefined;
}
