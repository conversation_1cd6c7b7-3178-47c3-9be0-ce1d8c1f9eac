import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';
import path from 'path';
import { existsSync, readFileSync } from 'fs';
import os from 'os';

export default defineConfig(({ mode }) => {
  // Load env file based on mode
  const env = loadEnv(mode, process.cwd());
  const isProduction = mode === 'production';

  // Get version from package.json
  const packageJson = JSON.parse(readFileSync('./package.json', 'utf8'));
  const version = packageJson.version;

  // Get hostname for certificate selection (auto-detect)
  const getHostname = () => {
    // Always use system hostname - no .env needed
    return os.hostname();
  };

  // Dynamic HTTPS certificate configuration
  const getHttpsConfig = (forPreview = false) => {
    if (isProduction && !forPreview) {
      return undefined; // No HTTPS config needed for production build (but allow for preview)
    }

    // Check if HTTP is forced (allow via environment or auto-fallback)
    if (env.VITE_FORCE_HTTP === 'true') {
      console.log('🌐 HTTP mode forced via VITE_FORCE_HTTP=true');
      return undefined;
    }

    const hostname = getHostname();

    // Certificate path options in order of preference
    const certOptions = [
      // 0. Custom paths from environment variables (highest priority)
      ...(env.VITE_SSL_KEY_PATH && env.VITE_SSL_CERT_PATH
        ? [
            {
              key: env.VITE_SSL_KEY_PATH,
              cert: env.VITE_SSL_CERT_PATH,
              name: 'custom (from env)',
            },
          ]
        : []),
      // 1. Hostname-specific certificates (mkcert generated)
      {
        key: `./certs/${hostname}-key.pem`,
        cert: `./certs/${hostname}.pem`,
        name: `${hostname} (mkcert)`,
      },
      // 2. Legacy hostname certificates
      {
        key: `./certs/${hostname}.local-key.pem`,
        cert: `./certs/${hostname}.local.pem`,
        name: `${hostname}.local (legacy)`,
      },
      // 3. Generic localhost certificates
      {
        key: './certs/localhost-key.pem',
        cert: './certs/localhost.pem',
        name: 'localhost (mkcert)',
      },
      // 4. SSL directory certificates (from scripts)
      {
        key: './ssl/dev/localhost.key',
        cert: './ssl/dev/localhost.crt',
        name: 'localhost (ssl/dev)',
      },
    ];

    // Find the first available certificate pair
    for (const option of certOptions) {
      if (existsSync(option.key) && existsSync(option.cert)) {
        console.log(`🔒 Using HTTPS certificates: ${option.name}`);
        return {
          key: readFileSync(option.key),
          cert: readFileSync(option.cert),
        };
      }
    }

    // No certificates found - auto-fallback to HTTP (no .env needed)
    console.warn('⚠️  No HTTPS certificates found - falling back to HTTP');
    console.warn('🔧 To enable HTTPS, run: npm run generate-certs');
    return undefined; // This will make Vite use HTTP
  };

  const hostname = getHostname();
  const protocol = getHttpsConfig() ? 'https' : 'http';
  const port = env.PORT ?? 3000;

  console.log('Environment loaded:', {
    mode,
    version,
    hostname,
  });

  // Show the correct URL after Vite's default output
  setTimeout(() => {
    console.log('');
    console.log(`🌐 Your application is available at:`);
    console.log(`   ➜ ${protocol}://${hostname}:${port}/`);
    console.log('');
  }, 1000);

  return {
    plugins: [
      react(),
      svgr({ svgrOptions: { icon: true } }),
      // Plugin to inject version into config.js at build time
      {
        name: 'inject-version-to-config',
        generateBundle() {
          // This will be handled by a separate build step or manual process
          // for now, we'll keep the version injection for backward compatibility
        },
      },
    ],
    define: {
      // Only keep NODE_ENV for build tools compatibility
      'process.env.NODE_ENV': JSON.stringify(mode),
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@components': '@/components',
        '@assets': '@/assets',
        '@utils': '@/utils',
        '@styles': '@/styles',
        '@shared': '@/components/shared',
      },
    },
    server: {
      host: '0.0.0.0',
      port: env.PORT ?? 3000,
      https: getHttpsConfig(),
    },
    preview: {
      host: '0.0.0.0',
      port: 3000,
      strictPort: false, // Allow Vite to use next available port if 3000 is busy
      https: getHttpsConfig(true), // Force HTTPS for preview server
      allowedHosts: [
        'localhost',
        '127.0.0.1',
        hostname,
        hostname.toLowerCase(),
        `${hostname}.local`,
        `${hostname.toLowerCase()}.local`,
        'abdullahchishtiweb',
        'AbdullahChishtiWeb',
      ],
    },
    build: {
      sourcemap: !isProduction,
      minify: isProduction,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
          },
        },
      },
    },
  };
});
