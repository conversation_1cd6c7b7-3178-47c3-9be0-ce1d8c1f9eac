import React from 'react';
import { t } from 'i18next';
import CloseButton from '../CloseButton/CloseButton';

interface OverlayProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  minWidth?: number;
  children: React.ReactNode;
}

const Dialog: React.FC<OverlayProps> = ({
  isOpen,
  onClose,
  title,
  subtitle,
  minWidth = 500,
  children,
}) => {
  if (!isOpen) return null;
  return (
    <div
      className={'fixed inset-0 flex items-center justify-center bg-semi-transparent z-[9999]'}
      onClick={onClose}
    >
      <div
        className={`p-large relative bg-background max-overlay-height max-overlay-width overflow-y-auto opacity-100 p-2 bg-background-color min-w-[${minWidth}px]`}
        onClick={e => e.stopPropagation()}
      >
        <CloseButton onClick={onClose} />
        <div className="p-6">
          {title && (
            <p className="text-xlarge font-bold uppercase">
              {t('common.about')} {t('common.serverProduct')}
            </p>
          )}
          {subtitle && <p className="text-medium font-bold">{subtitle}</p>}
          <div className="bg-secondary-background h-[2px] w-full mt-4 mb-8" />
          {children}
        </div>
      </div>
    </div>
  );
};

export default Dialog;
