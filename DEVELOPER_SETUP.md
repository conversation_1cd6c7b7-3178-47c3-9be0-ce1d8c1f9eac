# 🚀 Developer Setup Guide

This guide explains how to set up your local development environment using the new **runtime configuration system**.

## 📋 Quick Start

```bash
# 1. Clone and install dependencies
git clone [repository-url]
cd octoplant-web-application
npm install

# 2. Complete setup and start (one command!)
npm run fresh-start

# Or step by step:
# npm run generate-certs        # Generate SSL certificates
# npm run config development     # Set up development configuration
# npm start                      # Start development server
```

## 🔧 Configuration Management

### Available Configurations

- **Development** (`config.development.js`) - Local development with debug features
- **Staging** (`config.staging.js`) - Staging environment testing
- **Production** (`config.js`) - Production deployment

### Configuration Commands

```bash
# Switch between environments
npm run config development    # Switch to development config
npm run config staging       # Switch to staging config
npm run config production    # Switch to production config

# Utility commands
npm run config:list          # Show current configuration status
npm run config help          # Show configuration help
```

### Configuration Files

#### `public/config.js` (Application Configuration - The Only File You Need!)
Contains **all** application settings:
```javascript
window.APP_CONFIG = {
  // Backend URLs - UPDATE THESE FOR YOUR ENVIRONMENT
  API_URL: 'https://YourMachineName:5257/api/',
  OAUTH_URL: 'https://YourMachineName:64023/',
  STORAGE_URL: 'https://YourMachineName:5158/Files',
  
  // Environment settings
  ENVIRONMENT: 'development',
  DEBUG_MODE: true,
  EXPERIMENTAL: true,
  
  // ... other settings
};
```

## 🏗️ Development Workflow

### Setting Up a New Environment

1. **Copy the appropriate config template:**
   ```bash
   npm run config development  # For local development
   ```

2. **Edit `public/config.js` to match your backend:**
   ```javascript
   // Update these URLs to match your backend servers
   API_URL: 'https://YourMachineName:5257/api/',
   OAUTH_URL: 'https://YourMachineName:64023/',
   STORAGE_URL: 'https://YourMachineName:5158/Files',
   ```

3. **Start development:**
   ```bash
   npm start
   ```

### Switching Between Backends

To test against different backend environments:

```bash
# Test against local backend
npm run config development

# Test against staging backend
npm run config staging

# Test against production backend (be careful!)
npm run config production
```

### Creating Custom Configurations

1. **Copy an existing config:**
   ```bash
   cp public/config.development.js public/config.custom.js
   ```

2. **Edit the URLs and settings in `config.custom.js`**

3. **Use your custom config:**
   ```bash
   cp public/config.custom.js public/config.js
   npm start
   ```

## 🔍 Troubleshooting

### Common Issues

**❌ "Configuration not found" error**
- Make sure `public/config.js` exists
- Run `npm run config development` to create it

**❌ "VITE_HOSTNAME must be set" error**
- Copy `.env.example` to `.env`
- Set `VITE_HOSTNAME` to your machine name
- Run `npm run hostname` to find your hostname

**❌ SSL certificate errors**
- Run `npm run generate-certs` to create certificates
- Or add `VITE_ALLOW_HTTP=true` to `.env` for HTTP mode

**❌ API connection errors**
- Check that backend URLs in `public/config.js` are correct
- Verify your backend servers are running
- Check hostname case sensitivity (use exact machine name)

### Debug Commands

```bash
npm run config:list          # Check current configuration
npm run hostname             # Find your machine hostname
npm run generate-certs       # Generate SSL certificates
```

## 📁 File Structure

```
├── .env                     # Development server config (VITE_HOSTNAME, PORT)
├── .env.example            # Template for .env
├── public/
│   ├── config.js           # Active application configuration
│   ├── config.development.js  # Development template
│   ├── config.staging.js   # Staging template
│   └── config.production.js   # Production template (same as config.js)
└── scripts/
    └── config-manager.js   # Configuration management script
```

## 🎯 Key Differences from Old System

### ✅ New Runtime Configuration
- **Single source**: All app config in `public/config.js`
- **No rebuilds**: Change config and refresh browser
- **Environment switching**: Easy switching with npm commands
- **Customer-friendly**: Simple file editing for deployments

### ❌ Old Build-time Configuration
- **Multiple sources**: `.env` files with `VITE_` variables
- **Rebuild required**: Had to rebuild for config changes
- **Complex switching**: Manual `.env` file management
- **Technical deployment**: Required build knowledge

## 🚀 Next Steps

1. **Set up your local environment** using the Quick Start guide
2. **Test configuration switching** with different backends
3. **Customize your development config** as needed
4. **Share your setup** with other team members

For more help, run `npm run config help` or check the troubleshooting section above.
