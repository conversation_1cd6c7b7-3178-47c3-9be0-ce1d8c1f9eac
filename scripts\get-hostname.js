#!/usr/bin/env node

/**
 * Helper script to detect the current machine's hostname
 * Run with: npm run hostname
 */

import os from 'os';

console.log('🔍 Detecting your machine hostname...\n');

const hostname = os.hostname();
console.log(`✅ Your hostname is: ${hostname}`);
console.log(`\n📝 Add this to your .env file:`);
console.log(`VITE_HOSTNAME=${hostname}`);

console.log(`\n🌐 Your backend URLs should be:`);
console.log(`VITE_API_URL=https://${hostname}:5257/api/`);
console.log(`VITE_OAUTH_URL=https://${hostname}:64023/`);
console.log(`VITE_STORAGE_URL=https://${hostname}:5158/Files`);

console.log(`\n🚀 Your frontend will be accessible at:`);
console.log(`https://${hostname}:3000 (or next available port)`);
