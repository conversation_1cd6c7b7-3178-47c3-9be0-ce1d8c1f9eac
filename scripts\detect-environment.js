#!/usr/bin/env node

/**
 * Environment Detection and Validation
 * Detects current environment and validates configuration requirements
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';
import os from 'os';

class EnvironmentDetector {
  constructor() {
    this.hostname = process.env.VITE_HOSTNAME || os.hostname();
    this.platform = process.platform;
    this.nodeVersion = process.version;
  }

  detect() {
    console.log('🔍 Environment Detection Report\n');

    this.checkSystem();
    this.checkDependencies();
    this.checkCertificates();
    this.checkPorts();
    this.generateRecommendations();
  }

  checkSystem() {
    console.log('💻 System Information:');
    console.log(`  OS: ${this.platform}`);
    console.log(`  Node.js: ${this.nodeVersion}`);
    console.log(`  Hostname: ${this.hostname}`);
    console.log(`  Working Directory: ${process.cwd()}\n`);
  }

  checkDependencies() {
    console.log('📦 Dependencies:');

    // Check for required tools
    const tools = [
      { name: 'npm', command: 'npm --version', required: true },
      { name: 'mkcert', command: 'mkcert -version', required: false },
      { name: 'serve', command: 'npx serve --version', required: false },
    ];

    tools.forEach(tool => {
      try {
        const version = execSync(tool.command, { encoding: 'utf8', stdio: 'pipe' }).trim();
        console.log(`  ✅ ${tool.name}: ${version}`);
      } catch {
        if (tool.required) {
          console.log(`  ❌ ${tool.name}: Not found (REQUIRED)`);
        } else {
          console.log(`  ⚠️  ${tool.name}: Not found (optional)`);
        }
      }
    });
    console.log();
  }

  checkCertificates() {
    console.log('🔐 HTTPS Certificates:');

    const certPath = join('certs', `${this.hostname}.pem`);
    const keyPath = join('certs', `${this.hostname}-key.pem`);

    if (existsSync(certPath) && existsSync(keyPath)) {
      console.log(`  ✅ Certificates exist for ${this.hostname}`);
      console.log(`  📁 Cert: ${certPath}`);
      console.log(`  🔑 Key: ${keyPath}`);
    } else {
      console.log(`  ⚠️  No certificates found for ${this.hostname}`);
      console.log(`  📍 Expected: ${certPath}`);
    }
    console.log();
  }

  checkPorts() {
    console.log('🔌 Port Availability:');

    const ports = [3000, 5257, 64023, 5158];

    ports.forEach(port => {
      try {
        if (this.platform === 'win32') {
          execSync(`netstat -an | findstr :${port}`, { stdio: 'pipe' });
          console.log(`  ⚠️  Port ${port}: In use`);
        } else {
          execSync(`lsof -i :${port}`, { stdio: 'pipe' });
          console.log(`  ⚠️  Port ${port}: In use`);
        }
      } catch {
        console.log(`  ✅ Port ${port}: Available`);
      }
    });
    console.log();
  }

  generateRecommendations() {
    console.log('💡 Recommendations:');

    // Check if mkcert is available
    try {
      execSync('mkcert -version', { stdio: 'ignore' });
    } catch {
      console.log('  📝 Install mkcert for HTTPS support:');
      if (this.platform === 'win32') {
        console.log('     choco install mkcert');
      } else if (this.platform === 'darwin') {
        console.log('     brew install mkcert');
      } else {
        console.log('     See: https://github.com/FiloSottile/mkcert#installation');
      }
    }

    // Check if serve is installed
    try {
      execSync('npx serve --version', { stdio: 'ignore' });
    } catch {
      console.log('  📝 Install serve for production testing:');
      console.log('     npm install -g serve');
    }

    console.log('\n🚀 Ready to run automated production testing:');
    console.log('   npm run test:prod:auto');
  }
}

// Run detection
const detector = new EnvironmentDetector();
detector.detect();
